<template>
  <div class="graph-container">
    <VueFlow
      v-model:nodes="nodes"
      v-model:edges="edges"
      :node-types="nodeTypes"
      @nodes-change="onNodesChange"
      @edges-change="onEdgesChange"
      @connect="onConnect"
      @connect-start="onConnectStart"
      @connect-end="onConnectEnd"
      @pane-click="onPaneClick"
      @pane-context-menu="onPaneContextMenu"
      @node-click="onNodeClick"
      :default-viewport="{ zoom: 1 }"
      :min-zoom="0.2"
      :max-zoom="4"
      :connection-mode="'loose'"
      :delete-key-code="'Delete'"
      fit-view-on-init
      class="vue-flow-instance"
    >
      <Background pattern="dots" :gap="20" />
      <Controls />
      <MiniMap />
    </VueFlow>

    <!-- Context menu -->
    <div
      v-if="showContextMenu"
      class="context-menu"
      :style="{ left: contextMenuPos.x + 'px', top: contextMenuPos.y + 'px' }"
      @click.stop
    >
      <div class="context-menu-item" @click.stop="addNewNode">
        <i class="pi pi-plus"></i>
        Add Dialogue Node
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'
import { VueFlow, type Node, type Edge, type Connection } from '@vue-flow/core'
import { Background } from '@vue-flow/background'
import { Controls } from '@vue-flow/controls'
import { MiniMap } from '@vue-flow/minimap'
import DialogueNode from './DialogueNode.vue'
import { useDialogueStore } from '@/stores/dialogueStore'
import type { GraphNode, GraphEdge } from '@/types/dialogue'

// Import Vue Flow CSS
import '@vue-flow/core/dist/style.css'
import '@vue-flow/core/dist/theme-default.css'
import '@vue-flow/controls/dist/style.css'
import '@vue-flow/minimap/dist/style.css'

const emit = defineEmits<{
  editNode: [nodeId: string]
}>()

const store = useDialogueStore()

// Node types
const nodeTypes = {
  dialogue: DialogueNode
}

// Context menu
const showContextMenu = ref(false)
const contextMenuPos = ref({ x: 0, y: 0 })

// Connection dragging state
const connectionDragState = ref<{
  isReconnecting: boolean
  originalEdge: GraphEdge | null
  sourceNodeId: string | null
  sourceHandleId: string | null
}>({
  isReconnecting: false,
  originalEdge: null,
  sourceNodeId: null,
  sourceHandleId: null
})

// Convert store data to Vue Flow format
const nodes = ref<Node[]>([])
const edges = ref<Edge[]>([])

// Sync store → Vue Flow
watch(
  () => store.dialogueTree.nodes,
  (newNodes) => {
    nodes.value = newNodes.map(node => ({
      id: node.id,
      type: 'dialogue',
      position: node.position,
      data: { ...node.data, id: node.id },
      draggable: true,
      connectable: true,
      selectable: true
    }))
  },
  { immediate: true, deep: true }
)

watch(
  () => store.dialogueTree.edges,
  (newEdges) => {
    edges.value = newEdges.map(edge => ({
      id: edge.id,
      source: edge.source,
      target: edge.target,
      sourceHandle: edge.sourceHandle,
      targetHandle: edge.targetHandle,
      label: edge.label
    }))
  },
  { immediate: true, deep: true }
)

// Event handlers
function onNodesChange(changes: any[]) {
  changes.forEach(change => {
    if (change.type === 'position' && change.position) {
      const node = store.dialogueTree.nodes.find(n => n.id === change.id)
      if (node) {
        node.position = change.position
        store.isDirty = true
      }
    }
  })
}

function onEdgesChange(changes: any[]) {
  changes.forEach(change => {
    if (change.type === 'remove') {
      store.removeEdge(change.id)
    } else if (change.type === 'select') {
      // Handle edge selection for potential deletion
      console.log('Edge selected:', change.id)
    }
  })
}

function onConnect(connection: Connection) {
  if (connection.source && connection.target) {
    // If we're reconnecting, remove the original edge first
    if (connectionDragState.value.isReconnecting && connectionDragState.value.originalEdge) {
      store.removeEdge(connectionDragState.value.originalEdge.id)
    }

    const edge: GraphEdge = {
      id: `edge-${connection.source}-${connection.target}-${Date.now()}`,
      source: connection.source,
      target: connection.target,
      sourceHandle: connection.sourceHandle || undefined,
      targetHandle: connection.targetHandle || undefined
    }
    store.addEdge(edge)
  }

  // Reset connection drag state
  resetConnectionDragState()
}

function onConnectStart(event: any) {
  const { nodeId, handleId, handleType } = event

  // Check if this handle already has a connection
  const existingEdge = store.dialogueTree.edges.find(edge => {
    if (handleType === 'source') {
      return edge.source === nodeId && edge.sourceHandle === handleId
    } else {
      return edge.target === nodeId && edge.targetHandle === handleId
    }
  })

  if (existingEdge) {
    // We're starting a reconnection
    connectionDragState.value = {
      isReconnecting: true,
      originalEdge: existingEdge,
      sourceNodeId: nodeId,
      sourceHandleId: handleId
    }
  }
}

function onConnectEnd(event: any) {
  // If we were reconnecting but didn't connect to anything, delete the original edge
  if (connectionDragState.value.isReconnecting && connectionDragState.value.originalEdge) {
    // Check if the connection was completed (this will be handled in onConnect)
    // If not, we'll delete the edge after a short delay to allow onConnect to fire first
    setTimeout(() => {
      if (connectionDragState.value.isReconnecting && connectionDragState.value.originalEdge) {
        store.removeEdge(connectionDragState.value.originalEdge.id)
        resetConnectionDragState()
      }
    }, 100)
  }
}

function resetConnectionDragState() {
  connectionDragState.value = {
    isReconnecting: false,
    originalEdge: null,
    sourceNodeId: null,
    sourceHandleId: null
  }
}

function onPaneClick() {
  showContextMenu.value = false
  store.selectNode(null)
}

function onPaneContextMenu(event: any) {
  event.preventDefault()
  showContextMenu.value = true
  contextMenuPos.value = { x: event.clientX, y: event.clientY }
}

function onNodeClick(event: any) {
  showContextMenu.value = false
  store.selectNode(event.node.id)
}

function addNewNode() {
  // Hide context menu immediately
  showContextMenu.value = false

  const position = { x: 200, y: 200 } // cleaner than random placement

  const newNode: GraphNode = {
    id: `node-${Date.now()}`,
    type: 'dialogue',
    position,
    data: {
      Text: 'New dialogue node - click to edit',
      Id: `Node_${store.dialogueTree.nodes.length + 1}`
    }
  }

  store.addNode(newNode)

  nextTick(() => {
    store.selectNode(newNode.id)
    emit('editNode', newNode.id)
  })
}

// Handle clicks outside context menu
function handleClickOutside(event: MouseEvent) {
  if (showContextMenu.value) {
    const contextMenu = document.querySelector('.context-menu')
    if (contextMenu && event.target && !contextMenu.contains(event.target as Element)) {
      showContextMenu.value = false
    }
  }
}

// Close context menu on any click
function closeContextMenu() {
  showContextMenu.value = false
}

// Add global click listener
document.addEventListener('click', handleClickOutside)
document.addEventListener('contextmenu', (e) => {
  if (e.target && (e.target as Element).closest('.graph-container')) {
    e.preventDefault()
  }
})

// Close context menu on escape key
document.addEventListener('keydown', (e) => {
  if (e.key === 'Escape' && showContextMenu.value) {
    showContextMenu.value = false
  }
})
</script>

<style scoped>
.graph-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.vue-flow-instance {
  width: 100%;
  height: 100%;
  background: #1a1a1a;
}
.context-menu {
  position: fixed;
  background: #2d2d2d;
  border: 1px solid #4a5568;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  min-width: 160px;
}
.context-menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 12px;
  cursor: pointer;
  font-size: 14px;
  color: #e5e7eb;
  transition: background-color 0.15s ease;
}
.context-menu-item:hover {
  background-color: #374151;
}
.context-menu-item i {
  width: 16px;
  color: #9ca3af;
}
</style>

<style>
/* Vue Flow Dark Theme Overrides */
.vue-flow {
  background: #1a1a1a;
}

.vue-flow__node-dialogue {
  background: transparent;
  border: none;
  padding: 0;
}

.vue-flow__edge-path {
  stroke: #6b7280;
  stroke-width: 2;
}

.vue-flow__edge.selected .vue-flow__edge-path {
  stroke: #3b82f6;
  stroke-width: 3;
}

.vue-flow__connection-line {
  stroke: #3b82f6;
  stroke-width: 3;
  stroke-dasharray: 8,4;
  animation: dash 1s linear infinite;
}

@keyframes dash {
  to {
    stroke-dashoffset: -12;
  }
}

/* Connection dragging styles */
.vue-flow__handle {
  transition: all 0.2s ease;
}

.vue-flow__handle:hover {
  transform: scale(1.2);
}

.vue-flow__handle.connecting {
  transform: scale(1.4);
  box-shadow: 0 0 16px rgba(59, 130, 246, 0.8);
}

/* Edge hover and selection */
.vue-flow__edge:hover .vue-flow__edge-path {
  stroke: #60a5fa;
  stroke-width: 3;
  cursor: pointer;
}

.vue-flow__edge.selected .vue-flow__edge-path {
  stroke: #3b82f6;
  stroke-width: 4;
}

.vue-flow__controls {
  background: #2d2d2d !important;
  border: 1px solid #4a5568 !important;
  border-radius: 6px !important;
}

.vue-flow__controls button {
  background: transparent !important;
  border: none !important;
  color: #e5e7eb !important;
}

.vue-flow__controls button:hover {
  background: #374151 !important;
}

.vue-flow__minimap {
  background: #2d2d2d !important;
  border: 1px solid #4a5568 !important;
  border-radius: 6px !important;
}

.vue-flow__minimap-mask {
  fill: rgba(255, 255, 255, 0.1) !important;
}

.vue-flow__minimap-node {
  fill: #4a5568 !important;
}

/* Background pattern */
.vue-flow__background {
  background-color: #1a1a1a;
}

.vue-flow__background .vue-flow__background-pattern {
  fill: #374151;
  opacity: 0.4;
}
</style>

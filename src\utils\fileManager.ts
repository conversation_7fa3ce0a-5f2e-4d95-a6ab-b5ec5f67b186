import { saveAs } from 'file-saver'
import type { DialogueTree } from '@/types/dialogue'
import { LuaExporter } from './luaExporter'

export class FileManager {
  private static readonly FILE_VERSION = '1.0.0'

  static async saveDialogueTree(tree: DialogueTree, filename?: string): Promise<void> {
    try {
      const data = {
        version: this.FILE_VERSION,
        timestamp: new Date().toISOString(),
        ...tree
      }

      const jsonString = JSON.stringify(data, null, 2)
      const blob = new Blob([jsonString], { type: 'application/json' })
      const finalFilename = filename || `${tree.name.replace(/[^a-zA-Z0-9]/g, '_')}.json`
      
      saveAs(blob, finalFilename)
    } catch (error) {
      throw new Error(`Failed to save dialogue tree: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  static async loadDialogueTree(file: File): Promise<DialogueTree> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (event) => {
        try {
          const content = event.target?.result as string
          const data = JSON.parse(content)
          
          // Validate the loaded data
          if (!this.isValidDialogueTree(data)) {
            throw new Error('Invalid dialogue tree format')
          }

          // Extract the dialogue tree data (remove metadata)
          const tree: DialogueTree = {
            nodes: data.nodes || [],
            edges: data.edges || [],
            name: data.name || 'Untitled',
            description: data.description || '',
          }

          resolve(tree)
        } catch (error) {
          reject(new Error(`Failed to load dialogue tree: ${error instanceof Error ? error.message : 'Invalid JSON format'}`))
        }
      }

      reader.onerror = () => {
        reject(new Error('Failed to read file'))
      }

      reader.readAsText(file)
    })
  }

  static exportToLua(tree: DialogueTree, filename?: string): void {
    try {
      const exporter = new LuaExporter()
      const luaCode = exporter.exportDialogueTree(tree)
      
      const blob = new Blob([luaCode], { type: 'text/plain' })
      const finalFilename = filename || `${tree.name.replace(/[^a-zA-Z0-9]/g, '_')}.lua`
      
      saveAs(blob, finalFilename)
    } catch (error) {
      throw new Error(`Failed to export to Lua: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  static exportToJson(tree: DialogueTree, filename?: string): void {
    try {
      const jsonString = JSON.stringify(tree, null, 2)
      const blob = new Blob([jsonString], { type: 'application/json' })
      const finalFilename = filename || `${tree.name.replace(/[^a-zA-Z0-9]/g, '_')}_export.json`
      
      saveAs(blob, finalFilename)
    } catch (error) {
      throw new Error(`Failed to export to JSON: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private static isValidDialogueTree(data: any): boolean {
    if (!data || typeof data !== 'object') {
      return false
    }

    // Check required fields
    if (!Array.isArray(data.nodes) || !Array.isArray(data.edges)) {
      return false
    }

    // Validate nodes
    for (const node of data.nodes) {
      if (!node.id || !node.data || typeof node.data.Text !== 'string') {
        return false
      }
      
      if (!node.position || typeof node.position.x !== 'number' || typeof node.position.y !== 'number') {
        return false
      }
    }

    // Validate edges
    for (const edge of data.edges) {
      if (!edge.id || !edge.source || !edge.target) {
        return false
      }
    }

    return true
  }
}

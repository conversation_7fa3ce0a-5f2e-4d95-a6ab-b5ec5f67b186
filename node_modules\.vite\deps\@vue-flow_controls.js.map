{"version": 3, "sources": ["../../@vue-flow/controls/dist/vue-flow-controls.mjs"], "sourcesContent": ["import { openBlock, createElementBlock, renderSlot, createElementVNode, defineComponent, toRef, createBlock, unref, withCtx, Fragment, createVNode, resolveDynamicComponent, createCommentVNode } from \"vue\";\nimport { PanelPosition, useVueFlow, Panel } from \"@vue-flow/core\";\nconst _sfc_main$1 = {\n  name: \"ControlButton\",\n  compatConfig: { MODE: 3 }\n};\nconst _export_sfc = (sfc, props) => {\n  const target = sfc.__vccOpts || sfc;\n  for (const [key, val] of props) {\n    target[key] = val;\n  }\n  return target;\n};\nconst _hoisted_1$5 = {\n  type: \"button\",\n  class: \"vue-flow__controls-button\"\n};\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  return openBlock(), createElementBlock(\"button\", _hoisted_1$5, [\n    renderSlot(_ctx.$slots, \"default\")\n  ]);\n}\nconst ControlButton = /* @__PURE__ */ _export_sfc(_sfc_main$1, [[\"render\", _sfc_render]]);\nconst _hoisted_1$4 = {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  viewBox: \"0 0 32 32\"\n};\nconst _hoisted_2$4 = /* @__PURE__ */ createElementVNode(\"path\", { d: \"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z\" }, null, -1);\nconst _hoisted_3$4 = [\n  _hoisted_2$4\n];\nfunction render$4(_ctx, _cache) {\n  return openBlock(), createElementBlock(\"svg\", _hoisted_1$4, _hoisted_3$4);\n}\nconst PlusIcon = { render: render$4 };\nconst _hoisted_1$3 = {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  viewBox: \"0 0 32 5\"\n};\nconst _hoisted_2$3 = /* @__PURE__ */ createElementVNode(\"path\", { d: \"M0 0h32v4.2H0z\" }, null, -1);\nconst _hoisted_3$3 = [\n  _hoisted_2$3\n];\nfunction render$3(_ctx, _cache) {\n  return openBlock(), createElementBlock(\"svg\", _hoisted_1$3, _hoisted_3$3);\n}\nconst MinusIcon = { render: render$3 };\nconst _hoisted_1$2 = {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  viewBox: \"0 0 32 30\"\n};\nconst _hoisted_2$2 = /* @__PURE__ */ createElementVNode(\"path\", { d: \"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0 0 27.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94a.919.919 0 0 1-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z\" }, null, -1);\nconst _hoisted_3$2 = [\n  _hoisted_2$2\n];\nfunction render$2(_ctx, _cache) {\n  return openBlock(), createElementBlock(\"svg\", _hoisted_1$2, _hoisted_3$2);\n}\nconst FitView = { render: render$2 };\nconst _hoisted_1$1 = {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  viewBox: \"0 0 25 32\"\n};\nconst _hoisted_2$1 = /* @__PURE__ */ createElementVNode(\"path\", { d: \"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 0 0 0 13.714v15.238A3.056 3.056 0 0 0 3.048 32h18.285a3.056 3.056 0 0 0 3.048-3.048V13.714a3.056 3.056 0 0 0-3.048-3.047zM12.19 24.533a3.056 3.056 0 0 1-3.047-3.047 3.056 3.056 0 0 1 3.047-3.048 3.056 3.056 0 0 1 3.048 3.048 3.056 3.056 0 0 1-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z\" }, null, -1);\nconst _hoisted_3$1 = [\n  _hoisted_2$1\n];\nfunction render$1(_ctx, _cache) {\n  return openBlock(), createElementBlock(\"svg\", _hoisted_1$1, _hoisted_3$1);\n}\nconst Lock = { render: render$1 };\nconst _hoisted_1 = {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  viewBox: \"0 0 25 32\"\n};\nconst _hoisted_2 = /* @__PURE__ */ createElementVNode(\"path\", { d: \"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 0 0 0 13.714v15.238A3.056 3.056 0 0 0 3.048 32h18.285a3.056 3.056 0 0 0 3.048-3.048V13.714a3.056 3.056 0 0 0-3.048-3.047zM12.19 24.533a3.056 3.056 0 0 1-3.047-3.047 3.056 3.056 0 0 1 3.047-3.048 3.056 3.056 0 0 1 3.048 3.048 3.056 3.056 0 0 1-3.048 3.047z\" }, null, -1);\nconst _hoisted_3 = [\n  _hoisted_2\n];\nfunction render(_ctx, _cache) {\n  return openBlock(), createElementBlock(\"svg\", _hoisted_1, _hoisted_3);\n}\nconst Unlock = { render };\nconst __default__ = {\n  name: \"Controls\",\n  compatConfig: { MODE: 3 }\n};\nconst _sfc_main = /* @__PURE__ */ defineComponent({\n  ...__default__,\n  props: {\n    showZoom: { type: Boolean, default: true },\n    showFitView: { type: Boolean, default: true },\n    showInteractive: { type: Boolean, default: true },\n    fitViewParams: {},\n    position: { default: () => PanelPosition.BottomLeft }\n  },\n  emits: [\"zoomIn\", \"zoomOut\", \"fitView\", \"interactionChange\"],\n  setup(__props, { emit }) {\n    const {\n      nodesDraggable,\n      nodesConnectable,\n      elementsSelectable,\n      setInteractive,\n      zoomIn,\n      zoomOut,\n      fitView,\n      viewport,\n      minZoom,\n      maxZoom\n    } = useVueFlow();\n    const isInteractive = toRef(() => nodesDraggable.value || nodesConnectable.value || elementsSelectable.value);\n    const minZoomReached = toRef(() => viewport.value.zoom <= minZoom.value);\n    const maxZoomReached = toRef(() => viewport.value.zoom >= maxZoom.value);\n    function onZoomInHandler() {\n      zoomIn();\n      emit(\"zoomIn\");\n    }\n    function onZoomOutHandler() {\n      zoomOut();\n      emit(\"zoomOut\");\n    }\n    function onFitViewHandler() {\n      fitView(__props.fitViewParams);\n      emit(\"fitView\");\n    }\n    function onInteractiveChangeHandler() {\n      setInteractive(!isInteractive.value);\n      emit(\"interactionChange\", !isInteractive.value);\n    }\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(unref(Panel), {\n        class: \"vue-flow__controls\",\n        position: _ctx.position\n      }, {\n        default: withCtx(() => [\n          renderSlot(_ctx.$slots, \"top\"),\n          _ctx.showZoom ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [\n            renderSlot(_ctx.$slots, \"control-zoom-in\", {}, () => [\n              createVNode(ControlButton, {\n                class: \"vue-flow__controls-zoomin\",\n                disabled: maxZoomReached.value,\n                onClick: onZoomInHandler\n              }, {\n                default: withCtx(() => [\n                  renderSlot(_ctx.$slots, \"icon-zoom-in\", {}, () => [\n                    (openBlock(), createBlock(resolveDynamicComponent(unref(PlusIcon))))\n                  ])\n                ]),\n                _: 3\n              }, 8, [\"disabled\"])\n            ]),\n            renderSlot(_ctx.$slots, \"control-zoom-out\", {}, () => [\n              createVNode(ControlButton, {\n                class: \"vue-flow__controls-zoomout\",\n                disabled: minZoomReached.value,\n                onClick: onZoomOutHandler\n              }, {\n                default: withCtx(() => [\n                  renderSlot(_ctx.$slots, \"icon-zoom-out\", {}, () => [\n                    (openBlock(), createBlock(resolveDynamicComponent(unref(MinusIcon))))\n                  ])\n                ]),\n                _: 3\n              }, 8, [\"disabled\"])\n            ])\n          ], 64)) : createCommentVNode(\"\", true),\n          _ctx.showFitView ? renderSlot(_ctx.$slots, \"control-fit-view\", { key: 1 }, () => [\n            createVNode(ControlButton, {\n              class: \"vue-flow__controls-fitview\",\n              onClick: onFitViewHandler\n            }, {\n              default: withCtx(() => [\n                renderSlot(_ctx.$slots, \"icon-fit-view\", {}, () => [\n                  (openBlock(), createBlock(resolveDynamicComponent(unref(FitView))))\n                ])\n              ]),\n              _: 3\n            })\n          ]) : createCommentVNode(\"\", true),\n          _ctx.showInteractive ? renderSlot(_ctx.$slots, \"control-interactive\", { key: 2 }, () => [\n            _ctx.showInteractive ? (openBlock(), createBlock(ControlButton, {\n              key: 0,\n              class: \"vue-flow__controls-interactive\",\n              onClick: onInteractiveChangeHandler\n            }, {\n              default: withCtx(() => [\n                isInteractive.value ? renderSlot(_ctx.$slots, \"icon-unlock\", { key: 0 }, () => [\n                  (openBlock(), createBlock(resolveDynamicComponent(unref(Unlock))))\n                ]) : createCommentVNode(\"\", true),\n                !isInteractive.value ? renderSlot(_ctx.$slots, \"icon-lock\", { key: 1 }, () => [\n                  (openBlock(), createBlock(resolveDynamicComponent(unref(Lock))))\n                ]) : createCommentVNode(\"\", true)\n              ]),\n              _: 3\n            })) : createCommentVNode(\"\", true)\n          ]) : createCommentVNode(\"\", true),\n          renderSlot(_ctx.$slots, \"default\")\n        ]),\n        _: 3\n      }, 8, [\"position\"]);\n    };\n  }\n});\nexport {\n  ControlButton,\n  _sfc_main as Controls\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAM,cAAc;AAAA,EAClB,MAAM;AAAA,EACN,cAAc,EAAE,MAAM,EAAE;AAC1B;AACA,IAAM,cAAc,CAAC,KAAK,UAAU;AAClC,QAAM,SAAS,IAAI,aAAa;AAChC,aAAW,CAAC,KAAK,GAAG,KAAK,OAAO;AAC9B,WAAO,GAAG,IAAI;AAAA,EAChB;AACA,SAAO;AACT;AACA,IAAM,eAAe;AAAA,EACnB,MAAM;AAAA,EACN,OAAO;AACT;AACA,SAAS,YAAY,MAAM,QAAQ,QAAQ,QAAQ,OAAO,UAAU;AAClE,SAAO,UAAU,GAAG,mBAAmB,UAAU,cAAc;AAAA,IAC7D,WAAW,KAAK,QAAQ,SAAS;AAAA,EACnC,CAAC;AACH;AACA,IAAM,gBAAgC,YAAY,aAAa,CAAC,CAAC,UAAU,WAAW,CAAC,CAAC;AACxF,IAAM,eAAe;AAAA,EACnB,OAAO;AAAA,EACP,SAAS;AACX;AACA,IAAM,eAA+B,gBAAmB,QAAQ,EAAE,GAAG,wEAAwE,GAAG,MAAM,EAAE;AACxJ,IAAM,eAAe;AAAA,EACnB;AACF;AACA,SAAS,SAAS,MAAM,QAAQ;AAC9B,SAAO,UAAU,GAAG,mBAAmB,OAAO,cAAc,YAAY;AAC1E;AACA,IAAM,WAAW,EAAE,QAAQ,SAAS;AACpC,IAAM,eAAe;AAAA,EACnB,OAAO;AAAA,EACP,SAAS;AACX;AACA,IAAM,eAA+B,gBAAmB,QAAQ,EAAE,GAAG,iBAAiB,GAAG,MAAM,EAAE;AACjG,IAAM,eAAe;AAAA,EACnB;AACF;AACA,SAAS,SAAS,MAAM,QAAQ;AAC9B,SAAO,UAAU,GAAG,mBAAmB,OAAO,cAAc,YAAY;AAC1E;AACA,IAAM,YAAY,EAAE,QAAQ,SAAS;AACrC,IAAM,eAAe;AAAA,EACnB,OAAO;AAAA,EACP,SAAS;AACX;AACA,IAAM,eAA+B,gBAAmB,QAAQ,EAAE,GAAG,+XAA+X,GAAG,MAAM,EAAE;AAC/c,IAAM,eAAe;AAAA,EACnB;AACF;AACA,SAAS,SAAS,MAAM,QAAQ;AAC9B,SAAO,UAAU,GAAG,mBAAmB,OAAO,cAAc,YAAY;AAC1E;AACA,IAAM,UAAU,EAAE,QAAQ,SAAS;AACnC,IAAM,eAAe;AAAA,EACnB,OAAO;AAAA,EACP,SAAS;AACX;AACA,IAAM,eAA+B,gBAAmB,QAAQ,EAAE,GAAG,8cAA8c,GAAG,MAAM,EAAE;AAC9hB,IAAM,eAAe;AAAA,EACnB;AACF;AACA,SAAS,SAAS,MAAM,QAAQ;AAC9B,SAAO,UAAU,GAAG,mBAAmB,OAAO,cAAc,YAAY;AAC1E;AACA,IAAM,OAAO,EAAE,QAAQ,SAAS;AAChC,IAAM,aAAa;AAAA,EACjB,OAAO;AAAA,EACP,SAAS;AACX;AACA,IAAM,aAA6B,gBAAmB,QAAQ,EAAE,GAAG,oZAAoZ,GAAG,MAAM,EAAE;AACle,IAAM,aAAa;AAAA,EACjB;AACF;AACA,SAAS,OAAO,MAAM,QAAQ;AAC5B,SAAO,UAAU,GAAG,mBAAmB,OAAO,YAAY,UAAU;AACtE;AACA,IAAM,SAAS,EAAE,OAAO;AACxB,IAAM,cAAc;AAAA,EAClB,MAAM;AAAA,EACN,cAAc,EAAE,MAAM,EAAE;AAC1B;AACA,IAAMA,aAA4B,gBAAgB;AAAA,EAChD,GAAG;AAAA,EACH,OAAO;AAAA,IACL,UAAU,EAAE,MAAM,SAAS,SAAS,KAAK;AAAA,IACzC,aAAa,EAAE,MAAM,SAAS,SAAS,KAAK;AAAA,IAC5C,iBAAiB,EAAE,MAAM,SAAS,SAAS,KAAK;AAAA,IAChD,eAAe,CAAC;AAAA,IAChB,UAAU,EAAE,SAAS,MAAM,cAAc,WAAW;AAAA,EACtD;AAAA,EACA,OAAO,CAAC,UAAU,WAAW,WAAW,mBAAmB;AAAA,EAC3D,MAAM,SAAS,EAAE,KAAK,GAAG;AACvB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,WAAW;AACf,UAAM,gBAAgB,MAAM,MAAM,eAAe,SAAS,iBAAiB,SAAS,mBAAmB,KAAK;AAC5G,UAAM,iBAAiB,MAAM,MAAM,SAAS,MAAM,QAAQ,QAAQ,KAAK;AACvE,UAAM,iBAAiB,MAAM,MAAM,SAAS,MAAM,QAAQ,QAAQ,KAAK;AACvE,aAAS,kBAAkB;AACzB,aAAO;AACP,WAAK,QAAQ;AAAA,IACf;AACA,aAAS,mBAAmB;AAC1B,cAAQ;AACR,WAAK,SAAS;AAAA,IAChB;AACA,aAAS,mBAAmB;AAC1B,cAAQ,QAAQ,aAAa;AAC7B,WAAK,SAAS;AAAA,IAChB;AACA,aAAS,6BAA6B;AACpC,qBAAe,CAAC,cAAc,KAAK;AACnC,WAAK,qBAAqB,CAAC,cAAc,KAAK;AAAA,IAChD;AACA,WAAO,CAAC,MAAM,WAAW;AACvB,aAAO,UAAU,GAAG,YAAY,MAAM,SAAK,GAAG;AAAA,QAC5C,OAAO;AAAA,QACP,UAAU,KAAK;AAAA,MACjB,GAAG;AAAA,QACD,SAAS,QAAQ,MAAM;AAAA,UACrB,WAAW,KAAK,QAAQ,KAAK;AAAA,UAC7B,KAAK,YAAY,UAAU,GAAG,mBAAmB,UAAU,EAAE,KAAK,EAAE,GAAG;AAAA,YACrE,WAAW,KAAK,QAAQ,mBAAmB,CAAC,GAAG,MAAM;AAAA,cACnD,YAAY,eAAe;AAAA,gBACzB,OAAO;AAAA,gBACP,UAAU,eAAe;AAAA,gBACzB,SAAS;AAAA,cACX,GAAG;AAAA,gBACD,SAAS,QAAQ,MAAM;AAAA,kBACrB,WAAW,KAAK,QAAQ,gBAAgB,CAAC,GAAG,MAAM;AAAA,qBAC/C,UAAU,GAAG,YAAY,wBAAwB,MAAM,QAAQ,CAAC,CAAC;AAAA,kBACpE,CAAC;AAAA,gBACH,CAAC;AAAA,gBACD,GAAG;AAAA,cACL,GAAG,GAAG,CAAC,UAAU,CAAC;AAAA,YACpB,CAAC;AAAA,YACD,WAAW,KAAK,QAAQ,oBAAoB,CAAC,GAAG,MAAM;AAAA,cACpD,YAAY,eAAe;AAAA,gBACzB,OAAO;AAAA,gBACP,UAAU,eAAe;AAAA,gBACzB,SAAS;AAAA,cACX,GAAG;AAAA,gBACD,SAAS,QAAQ,MAAM;AAAA,kBACrB,WAAW,KAAK,QAAQ,iBAAiB,CAAC,GAAG,MAAM;AAAA,qBAChD,UAAU,GAAG,YAAY,wBAAwB,MAAM,SAAS,CAAC,CAAC;AAAA,kBACrE,CAAC;AAAA,gBACH,CAAC;AAAA,gBACD,GAAG;AAAA,cACL,GAAG,GAAG,CAAC,UAAU,CAAC;AAAA,YACpB,CAAC;AAAA,UACH,GAAG,EAAE,KAAK,mBAAmB,IAAI,IAAI;AAAA,UACrC,KAAK,cAAc,WAAW,KAAK,QAAQ,oBAAoB,EAAE,KAAK,EAAE,GAAG,MAAM;AAAA,YAC/E,YAAY,eAAe;AAAA,cACzB,OAAO;AAAA,cACP,SAAS;AAAA,YACX,GAAG;AAAA,cACD,SAAS,QAAQ,MAAM;AAAA,gBACrB,WAAW,KAAK,QAAQ,iBAAiB,CAAC,GAAG,MAAM;AAAA,mBAChD,UAAU,GAAG,YAAY,wBAAwB,MAAM,OAAO,CAAC,CAAC;AAAA,gBACnE,CAAC;AAAA,cACH,CAAC;AAAA,cACD,GAAG;AAAA,YACL,CAAC;AAAA,UACH,CAAC,IAAI,mBAAmB,IAAI,IAAI;AAAA,UAChC,KAAK,kBAAkB,WAAW,KAAK,QAAQ,uBAAuB,EAAE,KAAK,EAAE,GAAG,MAAM;AAAA,YACtF,KAAK,mBAAmB,UAAU,GAAG,YAAY,eAAe;AAAA,cAC9D,KAAK;AAAA,cACL,OAAO;AAAA,cACP,SAAS;AAAA,YACX,GAAG;AAAA,cACD,SAAS,QAAQ,MAAM;AAAA,gBACrB,cAAc,QAAQ,WAAW,KAAK,QAAQ,eAAe,EAAE,KAAK,EAAE,GAAG,MAAM;AAAA,mBAC5E,UAAU,GAAG,YAAY,wBAAwB,MAAM,MAAM,CAAC,CAAC;AAAA,gBAClE,CAAC,IAAI,mBAAmB,IAAI,IAAI;AAAA,gBAChC,CAAC,cAAc,QAAQ,WAAW,KAAK,QAAQ,aAAa,EAAE,KAAK,EAAE,GAAG,MAAM;AAAA,mBAC3E,UAAU,GAAG,YAAY,wBAAwB,MAAM,IAAI,CAAC,CAAC;AAAA,gBAChE,CAAC,IAAI,mBAAmB,IAAI,IAAI;AAAA,cAClC,CAAC;AAAA,cACD,GAAG;AAAA,YACL,CAAC,KAAK,mBAAmB,IAAI,IAAI;AAAA,UACnC,CAAC,IAAI,mBAAmB,IAAI,IAAI;AAAA,UAChC,WAAW,KAAK,QAAQ,SAAS;AAAA,QACnC,CAAC;AAAA,QACD,GAAG;AAAA,MACL,GAAG,GAAG,CAAC,UAAU,CAAC;AAAA,IACpB;AAAA,EACF;AACF,CAAC;", "names": ["_sfc_main"]}
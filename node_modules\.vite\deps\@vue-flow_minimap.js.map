{"version": 3, "sources": ["../../@vue-flow/minimap/dist/vue-flow-minimap.mjs"], "sourcesContent": ["import { defineComponent, inject, useAttrs, toRef, openBlock, createElementBlock, Fragment, unref, createBlock, resolveDynamicComponent, normalizeProps, mergeProps, createCommentVNode, useSlots, ref, provide, computed, watchEffect, normalizeClass, withCtx, toDisplayString, renderList, normalizeStyle, createElementVNode } from \"vue\";\nimport { useVueFlow, getRectOfNodes, getBoundsofRects, wheelDelta, Panel, isMacOs, getConnectedEdges } from \"@vue-flow/core\";\nvar noop = { value: () => {\n} };\nfunction dispatch() {\n  for (var i = 0, n = arguments.length, _ = {}, t; i < n; ++i) {\n    if (!(t = arguments[i] + \"\") || t in _ || /[\\s.]/.test(t)) throw new Error(\"illegal type: \" + t);\n    _[t] = [];\n  }\n  return new Dispatch(_);\n}\nfunction Dispatch(_) {\n  this._ = _;\n}\nfunction parseTypenames$1(typenames, types) {\n  return typenames.trim().split(/^|\\s+/).map(function(t) {\n    var name = \"\", i = t.indexOf(\".\");\n    if (i >= 0) name = t.slice(i + 1), t = t.slice(0, i);\n    if (t && !types.hasOwnProperty(t)) throw new Error(\"unknown type: \" + t);\n    return { type: t, name };\n  });\n}\nDispatch.prototype = dispatch.prototype = {\n  constructor: Dispatch,\n  on: function(typename, callback) {\n    var _ = this._, T = parseTypenames$1(typename + \"\", _), t, i = -1, n = T.length;\n    if (arguments.length < 2) {\n      while (++i < n) if ((t = (typename = T[i]).type) && (t = get$1(_[t], typename.name))) return t;\n      return;\n    }\n    if (callback != null && typeof callback !== \"function\") throw new Error(\"invalid callback: \" + callback);\n    while (++i < n) {\n      if (t = (typename = T[i]).type) _[t] = set$1(_[t], typename.name, callback);\n      else if (callback == null) for (t in _) _[t] = set$1(_[t], typename.name, null);\n    }\n    return this;\n  },\n  copy: function() {\n    var copy = {}, _ = this._;\n    for (var t in _) copy[t] = _[t].slice();\n    return new Dispatch(copy);\n  },\n  call: function(type, that) {\n    if ((n = arguments.length - 2) > 0) for (var args = new Array(n), i = 0, n, t; i < n; ++i) args[i] = arguments[i + 2];\n    if (!this._.hasOwnProperty(type)) throw new Error(\"unknown type: \" + type);\n    for (t = this._[type], i = 0, n = t.length; i < n; ++i) t[i].value.apply(that, args);\n  },\n  apply: function(type, that, args) {\n    if (!this._.hasOwnProperty(type)) throw new Error(\"unknown type: \" + type);\n    for (var t = this._[type], i = 0, n = t.length; i < n; ++i) t[i].value.apply(that, args);\n  }\n};\nfunction get$1(type, name) {\n  for (var i = 0, n = type.length, c; i < n; ++i) {\n    if ((c = type[i]).name === name) {\n      return c.value;\n    }\n  }\n}\nfunction set$1(type, name, callback) {\n  for (var i = 0, n = type.length; i < n; ++i) {\n    if (type[i].name === name) {\n      type[i] = noop, type = type.slice(0, i).concat(type.slice(i + 1));\n      break;\n    }\n  }\n  if (callback != null) type.push({ name, value: callback });\n  return type;\n}\nvar xhtml = \"http://www.w3.org/1999/xhtml\";\nconst namespaces = {\n  svg: \"http://www.w3.org/2000/svg\",\n  xhtml,\n  xlink: \"http://www.w3.org/1999/xlink\",\n  xml: \"http://www.w3.org/XML/1998/namespace\",\n  xmlns: \"http://www.w3.org/2000/xmlns/\"\n};\nfunction namespace(name) {\n  var prefix = name += \"\", i = prefix.indexOf(\":\");\n  if (i >= 0 && (prefix = name.slice(0, i)) !== \"xmlns\") name = name.slice(i + 1);\n  return namespaces.hasOwnProperty(prefix) ? { space: namespaces[prefix], local: name } : name;\n}\nfunction creatorInherit(name) {\n  return function() {\n    var document2 = this.ownerDocument, uri = this.namespaceURI;\n    return uri === xhtml && document2.documentElement.namespaceURI === xhtml ? document2.createElement(name) : document2.createElementNS(uri, name);\n  };\n}\nfunction creatorFixed(fullname) {\n  return function() {\n    return this.ownerDocument.createElementNS(fullname.space, fullname.local);\n  };\n}\nfunction creator(name) {\n  var fullname = namespace(name);\n  return (fullname.local ? creatorFixed : creatorInherit)(fullname);\n}\nfunction none() {\n}\nfunction selector(selector2) {\n  return selector2 == null ? none : function() {\n    return this.querySelector(selector2);\n  };\n}\nfunction selection_select(select2) {\n  if (typeof select2 !== \"function\") select2 = selector(select2);\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = new Array(n), node, subnode, i = 0; i < n; ++i) {\n      if ((node = group[i]) && (subnode = select2.call(node, node.__data__, i, group))) {\n        if (\"__data__\" in node) subnode.__data__ = node.__data__;\n        subgroup[i] = subnode;\n      }\n    }\n  }\n  return new Selection$1(subgroups, this._parents);\n}\nfunction array(x) {\n  return x == null ? [] : Array.isArray(x) ? x : Array.from(x);\n}\nfunction empty() {\n  return [];\n}\nfunction selectorAll(selector2) {\n  return selector2 == null ? empty : function() {\n    return this.querySelectorAll(selector2);\n  };\n}\nfunction arrayAll(select2) {\n  return function() {\n    return array(select2.apply(this, arguments));\n  };\n}\nfunction selection_selectAll(select2) {\n  if (typeof select2 === \"function\") select2 = arrayAll(select2);\n  else select2 = selectorAll(select2);\n  for (var groups = this._groups, m = groups.length, subgroups = [], parents = [], j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        subgroups.push(select2.call(node, node.__data__, i, group));\n        parents.push(node);\n      }\n    }\n  }\n  return new Selection$1(subgroups, parents);\n}\nfunction matcher(selector2) {\n  return function() {\n    return this.matches(selector2);\n  };\n}\nfunction childMatcher(selector2) {\n  return function(node) {\n    return node.matches(selector2);\n  };\n}\nvar find = Array.prototype.find;\nfunction childFind(match) {\n  return function() {\n    return find.call(this.children, match);\n  };\n}\nfunction childFirst() {\n  return this.firstElementChild;\n}\nfunction selection_selectChild(match) {\n  return this.select(match == null ? childFirst : childFind(typeof match === \"function\" ? match : childMatcher(match)));\n}\nvar filter = Array.prototype.filter;\nfunction children() {\n  return Array.from(this.children);\n}\nfunction childrenFilter(match) {\n  return function() {\n    return filter.call(this.children, match);\n  };\n}\nfunction selection_selectChildren(match) {\n  return this.selectAll(match == null ? children : childrenFilter(typeof match === \"function\" ? match : childMatcher(match)));\n}\nfunction selection_filter(match) {\n  if (typeof match !== \"function\") match = matcher(match);\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = [], node, i = 0; i < n; ++i) {\n      if ((node = group[i]) && match.call(node, node.__data__, i, group)) {\n        subgroup.push(node);\n      }\n    }\n  }\n  return new Selection$1(subgroups, this._parents);\n}\nfunction sparse(update) {\n  return new Array(update.length);\n}\nfunction selection_enter() {\n  return new Selection$1(this._enter || this._groups.map(sparse), this._parents);\n}\nfunction EnterNode(parent, datum2) {\n  this.ownerDocument = parent.ownerDocument;\n  this.namespaceURI = parent.namespaceURI;\n  this._next = null;\n  this._parent = parent;\n  this.__data__ = datum2;\n}\nEnterNode.prototype = {\n  constructor: EnterNode,\n  appendChild: function(child) {\n    return this._parent.insertBefore(child, this._next);\n  },\n  insertBefore: function(child, next) {\n    return this._parent.insertBefore(child, next);\n  },\n  querySelector: function(selector2) {\n    return this._parent.querySelector(selector2);\n  },\n  querySelectorAll: function(selector2) {\n    return this._parent.querySelectorAll(selector2);\n  }\n};\nfunction constant$2(x) {\n  return function() {\n    return x;\n  };\n}\nfunction bindIndex(parent, group, enter, update, exit, data) {\n  var i = 0, node, groupLength = group.length, dataLength = data.length;\n  for (; i < dataLength; ++i) {\n    if (node = group[i]) {\n      node.__data__ = data[i];\n      update[i] = node;\n    } else {\n      enter[i] = new EnterNode(parent, data[i]);\n    }\n  }\n  for (; i < groupLength; ++i) {\n    if (node = group[i]) {\n      exit[i] = node;\n    }\n  }\n}\nfunction bindKey(parent, group, enter, update, exit, data, key) {\n  var i, node, nodeByKeyValue = /* @__PURE__ */ new Map(), groupLength = group.length, dataLength = data.length, keyValues = new Array(groupLength), keyValue;\n  for (i = 0; i < groupLength; ++i) {\n    if (node = group[i]) {\n      keyValues[i] = keyValue = key.call(node, node.__data__, i, group) + \"\";\n      if (nodeByKeyValue.has(keyValue)) {\n        exit[i] = node;\n      } else {\n        nodeByKeyValue.set(keyValue, node);\n      }\n    }\n  }\n  for (i = 0; i < dataLength; ++i) {\n    keyValue = key.call(parent, data[i], i, data) + \"\";\n    if (node = nodeByKeyValue.get(keyValue)) {\n      update[i] = node;\n      node.__data__ = data[i];\n      nodeByKeyValue.delete(keyValue);\n    } else {\n      enter[i] = new EnterNode(parent, data[i]);\n    }\n  }\n  for (i = 0; i < groupLength; ++i) {\n    if ((node = group[i]) && nodeByKeyValue.get(keyValues[i]) === node) {\n      exit[i] = node;\n    }\n  }\n}\nfunction datum(node) {\n  return node.__data__;\n}\nfunction selection_data(value, key) {\n  if (!arguments.length) return Array.from(this, datum);\n  var bind = key ? bindKey : bindIndex, parents = this._parents, groups = this._groups;\n  if (typeof value !== \"function\") value = constant$2(value);\n  for (var m = groups.length, update = new Array(m), enter = new Array(m), exit = new Array(m), j = 0; j < m; ++j) {\n    var parent = parents[j], group = groups[j], groupLength = group.length, data = arraylike(value.call(parent, parent && parent.__data__, j, parents)), dataLength = data.length, enterGroup = enter[j] = new Array(dataLength), updateGroup = update[j] = new Array(dataLength), exitGroup = exit[j] = new Array(groupLength);\n    bind(parent, group, enterGroup, updateGroup, exitGroup, data, key);\n    for (var i0 = 0, i1 = 0, previous, next; i0 < dataLength; ++i0) {\n      if (previous = enterGroup[i0]) {\n        if (i0 >= i1) i1 = i0 + 1;\n        while (!(next = updateGroup[i1]) && ++i1 < dataLength) ;\n        previous._next = next || null;\n      }\n    }\n  }\n  update = new Selection$1(update, parents);\n  update._enter = enter;\n  update._exit = exit;\n  return update;\n}\nfunction arraylike(data) {\n  return typeof data === \"object\" && \"length\" in data ? data : Array.from(data);\n}\nfunction selection_exit() {\n  return new Selection$1(this._exit || this._groups.map(sparse), this._parents);\n}\nfunction selection_join(onenter, onupdate, onexit) {\n  var enter = this.enter(), update = this, exit = this.exit();\n  if (typeof onenter === \"function\") {\n    enter = onenter(enter);\n    if (enter) enter = enter.selection();\n  } else {\n    enter = enter.append(onenter + \"\");\n  }\n  if (onupdate != null) {\n    update = onupdate(update);\n    if (update) update = update.selection();\n  }\n  if (onexit == null) exit.remove();\n  else onexit(exit);\n  return enter && update ? enter.merge(update).order() : update;\n}\nfunction selection_merge(context) {\n  var selection2 = context.selection ? context.selection() : context;\n  for (var groups0 = this._groups, groups1 = selection2._groups, m0 = groups0.length, m1 = groups1.length, m = Math.min(m0, m1), merges = new Array(m0), j = 0; j < m; ++j) {\n    for (var group0 = groups0[j], group1 = groups1[j], n = group0.length, merge = merges[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group0[i] || group1[i]) {\n        merge[i] = node;\n      }\n    }\n  }\n  for (; j < m0; ++j) {\n    merges[j] = groups0[j];\n  }\n  return new Selection$1(merges, this._parents);\n}\nfunction selection_order() {\n  for (var groups = this._groups, j = -1, m = groups.length; ++j < m; ) {\n    for (var group = groups[j], i = group.length - 1, next = group[i], node; --i >= 0; ) {\n      if (node = group[i]) {\n        if (next && node.compareDocumentPosition(next) ^ 4) next.parentNode.insertBefore(node, next);\n        next = node;\n      }\n    }\n  }\n  return this;\n}\nfunction selection_sort(compare) {\n  if (!compare) compare = ascending;\n  function compareNode(a, b) {\n    return a && b ? compare(a.__data__, b.__data__) : !a - !b;\n  }\n  for (var groups = this._groups, m = groups.length, sortgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, sortgroup = sortgroups[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        sortgroup[i] = node;\n      }\n    }\n    sortgroup.sort(compareNode);\n  }\n  return new Selection$1(sortgroups, this._parents).order();\n}\nfunction ascending(a, b) {\n  return a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\nfunction selection_call() {\n  var callback = arguments[0];\n  arguments[0] = this;\n  callback.apply(null, arguments);\n  return this;\n}\nfunction selection_nodes() {\n  return Array.from(this);\n}\nfunction selection_node() {\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length; i < n; ++i) {\n      var node = group[i];\n      if (node) return node;\n    }\n  }\n  return null;\n}\nfunction selection_size() {\n  let size = 0;\n  for (const node of this) ++size;\n  return size;\n}\nfunction selection_empty() {\n  return !this.node();\n}\nfunction selection_each(callback) {\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length, node; i < n; ++i) {\n      if (node = group[i]) callback.call(node, node.__data__, i, group);\n    }\n  }\n  return this;\n}\nfunction attrRemove$1(name) {\n  return function() {\n    this.removeAttribute(name);\n  };\n}\nfunction attrRemoveNS$1(fullname) {\n  return function() {\n    this.removeAttributeNS(fullname.space, fullname.local);\n  };\n}\nfunction attrConstant$1(name, value) {\n  return function() {\n    this.setAttribute(name, value);\n  };\n}\nfunction attrConstantNS$1(fullname, value) {\n  return function() {\n    this.setAttributeNS(fullname.space, fullname.local, value);\n  };\n}\nfunction attrFunction$1(name, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.removeAttribute(name);\n    else this.setAttribute(name, v);\n  };\n}\nfunction attrFunctionNS$1(fullname, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.removeAttributeNS(fullname.space, fullname.local);\n    else this.setAttributeNS(fullname.space, fullname.local, v);\n  };\n}\nfunction selection_attr(name, value) {\n  var fullname = namespace(name);\n  if (arguments.length < 2) {\n    var node = this.node();\n    return fullname.local ? node.getAttributeNS(fullname.space, fullname.local) : node.getAttribute(fullname);\n  }\n  return this.each((value == null ? fullname.local ? attrRemoveNS$1 : attrRemove$1 : typeof value === \"function\" ? fullname.local ? attrFunctionNS$1 : attrFunction$1 : fullname.local ? attrConstantNS$1 : attrConstant$1)(fullname, value));\n}\nfunction defaultView(node) {\n  return node.ownerDocument && node.ownerDocument.defaultView || node.document && node || node.defaultView;\n}\nfunction styleRemove$1(name) {\n  return function() {\n    this.style.removeProperty(name);\n  };\n}\nfunction styleConstant$1(name, value, priority) {\n  return function() {\n    this.style.setProperty(name, value, priority);\n  };\n}\nfunction styleFunction$1(name, value, priority) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.style.removeProperty(name);\n    else this.style.setProperty(name, v, priority);\n  };\n}\nfunction selection_style(name, value, priority) {\n  return arguments.length > 1 ? this.each((value == null ? styleRemove$1 : typeof value === \"function\" ? styleFunction$1 : styleConstant$1)(name, value, priority == null ? \"\" : priority)) : styleValue(this.node(), name);\n}\nfunction styleValue(node, name) {\n  return node.style.getPropertyValue(name) || defaultView(node).getComputedStyle(node, null).getPropertyValue(name);\n}\nfunction propertyRemove(name) {\n  return function() {\n    delete this[name];\n  };\n}\nfunction propertyConstant(name, value) {\n  return function() {\n    this[name] = value;\n  };\n}\nfunction propertyFunction(name, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) delete this[name];\n    else this[name] = v;\n  };\n}\nfunction selection_property(name, value) {\n  return arguments.length > 1 ? this.each((value == null ? propertyRemove : typeof value === \"function\" ? propertyFunction : propertyConstant)(name, value)) : this.node()[name];\n}\nfunction classArray(string) {\n  return string.trim().split(/^|\\s+/);\n}\nfunction classList(node) {\n  return node.classList || new ClassList(node);\n}\nfunction ClassList(node) {\n  this._node = node;\n  this._names = classArray(node.getAttribute(\"class\") || \"\");\n}\nClassList.prototype = {\n  add: function(name) {\n    var i = this._names.indexOf(name);\n    if (i < 0) {\n      this._names.push(name);\n      this._node.setAttribute(\"class\", this._names.join(\" \"));\n    }\n  },\n  remove: function(name) {\n    var i = this._names.indexOf(name);\n    if (i >= 0) {\n      this._names.splice(i, 1);\n      this._node.setAttribute(\"class\", this._names.join(\" \"));\n    }\n  },\n  contains: function(name) {\n    return this._names.indexOf(name) >= 0;\n  }\n};\nfunction classedAdd(node, names) {\n  var list = classList(node), i = -1, n = names.length;\n  while (++i < n) list.add(names[i]);\n}\nfunction classedRemove(node, names) {\n  var list = classList(node), i = -1, n = names.length;\n  while (++i < n) list.remove(names[i]);\n}\nfunction classedTrue(names) {\n  return function() {\n    classedAdd(this, names);\n  };\n}\nfunction classedFalse(names) {\n  return function() {\n    classedRemove(this, names);\n  };\n}\nfunction classedFunction(names, value) {\n  return function() {\n    (value.apply(this, arguments) ? classedAdd : classedRemove)(this, names);\n  };\n}\nfunction selection_classed(name, value) {\n  var names = classArray(name + \"\");\n  if (arguments.length < 2) {\n    var list = classList(this.node()), i = -1, n = names.length;\n    while (++i < n) if (!list.contains(names[i])) return false;\n    return true;\n  }\n  return this.each((typeof value === \"function\" ? classedFunction : value ? classedTrue : classedFalse)(names, value));\n}\nfunction textRemove() {\n  this.textContent = \"\";\n}\nfunction textConstant$1(value) {\n  return function() {\n    this.textContent = value;\n  };\n}\nfunction textFunction$1(value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    this.textContent = v == null ? \"\" : v;\n  };\n}\nfunction selection_text(value) {\n  return arguments.length ? this.each(value == null ? textRemove : (typeof value === \"function\" ? textFunction$1 : textConstant$1)(value)) : this.node().textContent;\n}\nfunction htmlRemove() {\n  this.innerHTML = \"\";\n}\nfunction htmlConstant(value) {\n  return function() {\n    this.innerHTML = value;\n  };\n}\nfunction htmlFunction(value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    this.innerHTML = v == null ? \"\" : v;\n  };\n}\nfunction selection_html(value) {\n  return arguments.length ? this.each(value == null ? htmlRemove : (typeof value === \"function\" ? htmlFunction : htmlConstant)(value)) : this.node().innerHTML;\n}\nfunction raise() {\n  if (this.nextSibling) this.parentNode.appendChild(this);\n}\nfunction selection_raise() {\n  return this.each(raise);\n}\nfunction lower() {\n  if (this.previousSibling) this.parentNode.insertBefore(this, this.parentNode.firstChild);\n}\nfunction selection_lower() {\n  return this.each(lower);\n}\nfunction selection_append(name) {\n  var create2 = typeof name === \"function\" ? name : creator(name);\n  return this.select(function() {\n    return this.appendChild(create2.apply(this, arguments));\n  });\n}\nfunction constantNull() {\n  return null;\n}\nfunction selection_insert(name, before) {\n  var create2 = typeof name === \"function\" ? name : creator(name), select2 = before == null ? constantNull : typeof before === \"function\" ? before : selector(before);\n  return this.select(function() {\n    return this.insertBefore(create2.apply(this, arguments), select2.apply(this, arguments) || null);\n  });\n}\nfunction remove() {\n  var parent = this.parentNode;\n  if (parent) parent.removeChild(this);\n}\nfunction selection_remove() {\n  return this.each(remove);\n}\nfunction selection_cloneShallow() {\n  var clone = this.cloneNode(false), parent = this.parentNode;\n  return parent ? parent.insertBefore(clone, this.nextSibling) : clone;\n}\nfunction selection_cloneDeep() {\n  var clone = this.cloneNode(true), parent = this.parentNode;\n  return parent ? parent.insertBefore(clone, this.nextSibling) : clone;\n}\nfunction selection_clone(deep) {\n  return this.select(deep ? selection_cloneDeep : selection_cloneShallow);\n}\nfunction selection_datum(value) {\n  return arguments.length ? this.property(\"__data__\", value) : this.node().__data__;\n}\nfunction contextListener(listener) {\n  return function(event) {\n    listener.call(this, event, this.__data__);\n  };\n}\nfunction parseTypenames(typenames) {\n  return typenames.trim().split(/^|\\s+/).map(function(t) {\n    var name = \"\", i = t.indexOf(\".\");\n    if (i >= 0) name = t.slice(i + 1), t = t.slice(0, i);\n    return { type: t, name };\n  });\n}\nfunction onRemove(typename) {\n  return function() {\n    var on = this.__on;\n    if (!on) return;\n    for (var j = 0, i = -1, m = on.length, o; j < m; ++j) {\n      if (o = on[j], (!typename.type || o.type === typename.type) && o.name === typename.name) {\n        this.removeEventListener(o.type, o.listener, o.options);\n      } else {\n        on[++i] = o;\n      }\n    }\n    if (++i) on.length = i;\n    else delete this.__on;\n  };\n}\nfunction onAdd(typename, value, options) {\n  return function() {\n    var on = this.__on, o, listener = contextListener(value);\n    if (on) for (var j = 0, m = on.length; j < m; ++j) {\n      if ((o = on[j]).type === typename.type && o.name === typename.name) {\n        this.removeEventListener(o.type, o.listener, o.options);\n        this.addEventListener(o.type, o.listener = listener, o.options = options);\n        o.value = value;\n        return;\n      }\n    }\n    this.addEventListener(typename.type, listener, options);\n    o = { type: typename.type, name: typename.name, value, listener, options };\n    if (!on) this.__on = [o];\n    else on.push(o);\n  };\n}\nfunction selection_on(typename, value, options) {\n  var typenames = parseTypenames(typename + \"\"), i, n = typenames.length, t;\n  if (arguments.length < 2) {\n    var on = this.node().__on;\n    if (on) for (var j = 0, m = on.length, o; j < m; ++j) {\n      for (i = 0, o = on[j]; i < n; ++i) {\n        if ((t = typenames[i]).type === o.type && t.name === o.name) {\n          return o.value;\n        }\n      }\n    }\n    return;\n  }\n  on = value ? onAdd : onRemove;\n  for (i = 0; i < n; ++i) this.each(on(typenames[i], value, options));\n  return this;\n}\nfunction dispatchEvent(node, type, params) {\n  var window2 = defaultView(node), event = window2.CustomEvent;\n  if (typeof event === \"function\") {\n    event = new event(type, params);\n  } else {\n    event = window2.document.createEvent(\"Event\");\n    if (params) event.initEvent(type, params.bubbles, params.cancelable), event.detail = params.detail;\n    else event.initEvent(type, false, false);\n  }\n  node.dispatchEvent(event);\n}\nfunction dispatchConstant(type, params) {\n  return function() {\n    return dispatchEvent(this, type, params);\n  };\n}\nfunction dispatchFunction(type, params) {\n  return function() {\n    return dispatchEvent(this, type, params.apply(this, arguments));\n  };\n}\nfunction selection_dispatch(type, params) {\n  return this.each((typeof params === \"function\" ? dispatchFunction : dispatchConstant)(type, params));\n}\nfunction* selection_iterator() {\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length, node; i < n; ++i) {\n      if (node = group[i]) yield node;\n    }\n  }\n}\nvar root = [null];\nfunction Selection$1(groups, parents) {\n  this._groups = groups;\n  this._parents = parents;\n}\nfunction selection() {\n  return new Selection$1([[document.documentElement]], root);\n}\nfunction selection_selection() {\n  return this;\n}\nSelection$1.prototype = selection.prototype = {\n  constructor: Selection$1,\n  select: selection_select,\n  selectAll: selection_selectAll,\n  selectChild: selection_selectChild,\n  selectChildren: selection_selectChildren,\n  filter: selection_filter,\n  data: selection_data,\n  enter: selection_enter,\n  exit: selection_exit,\n  join: selection_join,\n  merge: selection_merge,\n  selection: selection_selection,\n  order: selection_order,\n  sort: selection_sort,\n  call: selection_call,\n  nodes: selection_nodes,\n  node: selection_node,\n  size: selection_size,\n  empty: selection_empty,\n  each: selection_each,\n  attr: selection_attr,\n  style: selection_style,\n  property: selection_property,\n  classed: selection_classed,\n  text: selection_text,\n  html: selection_html,\n  raise: selection_raise,\n  lower: selection_lower,\n  append: selection_append,\n  insert: selection_insert,\n  remove: selection_remove,\n  clone: selection_clone,\n  datum: selection_datum,\n  on: selection_on,\n  dispatch: selection_dispatch,\n  [Symbol.iterator]: selection_iterator\n};\nfunction select(selector2) {\n  return typeof selector2 === \"string\" ? new Selection$1([[document.querySelector(selector2)]], [document.documentElement]) : new Selection$1([[selector2]], root);\n}\nfunction sourceEvent(event) {\n  let sourceEvent2;\n  while (sourceEvent2 = event.sourceEvent) event = sourceEvent2;\n  return event;\n}\nfunction pointer(event, node) {\n  event = sourceEvent(event);\n  if (node === void 0) node = event.currentTarget;\n  if (node) {\n    var svg = node.ownerSVGElement || node;\n    if (svg.createSVGPoint) {\n      var point = svg.createSVGPoint();\n      point.x = event.clientX, point.y = event.clientY;\n      point = point.matrixTransform(node.getScreenCTM().inverse());\n      return [point.x, point.y];\n    }\n    if (node.getBoundingClientRect) {\n      var rect = node.getBoundingClientRect();\n      return [event.clientX - rect.left - node.clientLeft, event.clientY - rect.top - node.clientTop];\n    }\n  }\n  return [event.pageX, event.pageY];\n}\nconst nonpassivecapture = { capture: true, passive: false };\nfunction noevent$1(event) {\n  event.preventDefault();\n  event.stopImmediatePropagation();\n}\nfunction dragDisable(view) {\n  var root2 = view.document.documentElement, selection2 = select(view).on(\"dragstart.drag\", noevent$1, nonpassivecapture);\n  if (\"onselectstart\" in root2) {\n    selection2.on(\"selectstart.drag\", noevent$1, nonpassivecapture);\n  } else {\n    root2.__noselect = root2.style.MozUserSelect;\n    root2.style.MozUserSelect = \"none\";\n  }\n}\nfunction yesdrag(view, noclick) {\n  var root2 = view.document.documentElement, selection2 = select(view).on(\"dragstart.drag\", null);\n  if (noclick) {\n    selection2.on(\"click.drag\", noevent$1, nonpassivecapture);\n    setTimeout(function() {\n      selection2.on(\"click.drag\", null);\n    }, 0);\n  }\n  if (\"onselectstart\" in root2) {\n    selection2.on(\"selectstart.drag\", null);\n  } else {\n    root2.style.MozUserSelect = root2.__noselect;\n    delete root2.__noselect;\n  }\n}\nfunction define(constructor, factory, prototype) {\n  constructor.prototype = factory.prototype = prototype;\n  prototype.constructor = constructor;\n}\nfunction extend(parent, definition) {\n  var prototype = Object.create(parent.prototype);\n  for (var key in definition) prototype[key] = definition[key];\n  return prototype;\n}\nfunction Color() {\n}\nvar darker = 0.7;\nvar brighter = 1 / darker;\nvar reI = \"\\\\s*([+-]?\\\\d+)\\\\s*\", reN = \"\\\\s*([+-]?(?:\\\\d*\\\\.)?\\\\d+(?:[eE][+-]?\\\\d+)?)\\\\s*\", reP = \"\\\\s*([+-]?(?:\\\\d*\\\\.)?\\\\d+(?:[eE][+-]?\\\\d+)?)%\\\\s*\", reHex = /^#([0-9a-f]{3,8})$/, reRgbInteger = new RegExp(`^rgb\\\\(${reI},${reI},${reI}\\\\)$`), reRgbPercent = new RegExp(`^rgb\\\\(${reP},${reP},${reP}\\\\)$`), reRgbaInteger = new RegExp(`^rgba\\\\(${reI},${reI},${reI},${reN}\\\\)$`), reRgbaPercent = new RegExp(`^rgba\\\\(${reP},${reP},${reP},${reN}\\\\)$`), reHslPercent = new RegExp(`^hsl\\\\(${reN},${reP},${reP}\\\\)$`), reHslaPercent = new RegExp(`^hsla\\\\(${reN},${reP},${reP},${reN}\\\\)$`);\nvar named = {\n  aliceblue: 15792383,\n  antiquewhite: 16444375,\n  aqua: 65535,\n  aquamarine: 8388564,\n  azure: 15794175,\n  beige: 16119260,\n  bisque: 16770244,\n  black: 0,\n  blanchedalmond: 16772045,\n  blue: 255,\n  blueviolet: 9055202,\n  brown: 10824234,\n  burlywood: 14596231,\n  cadetblue: 6266528,\n  chartreuse: 8388352,\n  chocolate: 13789470,\n  coral: 16744272,\n  cornflowerblue: 6591981,\n  cornsilk: 16775388,\n  crimson: 14423100,\n  cyan: 65535,\n  darkblue: 139,\n  darkcyan: 35723,\n  darkgoldenrod: 12092939,\n  darkgray: 11119017,\n  darkgreen: 25600,\n  darkgrey: 11119017,\n  darkkhaki: 12433259,\n  darkmagenta: 9109643,\n  darkolivegreen: 5597999,\n  darkorange: 16747520,\n  darkorchid: 10040012,\n  darkred: 9109504,\n  darksalmon: 15308410,\n  darkseagreen: 9419919,\n  darkslateblue: 4734347,\n  darkslategray: 3100495,\n  darkslategrey: 3100495,\n  darkturquoise: 52945,\n  darkviolet: 9699539,\n  deeppink: 16716947,\n  deepskyblue: 49151,\n  dimgray: 6908265,\n  dimgrey: 6908265,\n  dodgerblue: 2003199,\n  firebrick: 11674146,\n  floralwhite: 16775920,\n  forestgreen: 2263842,\n  fuchsia: 16711935,\n  gainsboro: 14474460,\n  ghostwhite: 16316671,\n  gold: 16766720,\n  goldenrod: 14329120,\n  gray: 8421504,\n  green: 32768,\n  greenyellow: 11403055,\n  grey: 8421504,\n  honeydew: 15794160,\n  hotpink: 16738740,\n  indianred: 13458524,\n  indigo: 4915330,\n  ivory: 16777200,\n  khaki: 15787660,\n  lavender: 15132410,\n  lavenderblush: 16773365,\n  lawngreen: 8190976,\n  lemonchiffon: 16775885,\n  lightblue: 11393254,\n  lightcoral: 15761536,\n  lightcyan: 14745599,\n  lightgoldenrodyellow: 16448210,\n  lightgray: 13882323,\n  lightgreen: 9498256,\n  lightgrey: 13882323,\n  lightpink: 16758465,\n  lightsalmon: 16752762,\n  lightseagreen: 2142890,\n  lightskyblue: 8900346,\n  lightslategray: 7833753,\n  lightslategrey: 7833753,\n  lightsteelblue: 11584734,\n  lightyellow: 16777184,\n  lime: 65280,\n  limegreen: 3329330,\n  linen: 16445670,\n  magenta: 16711935,\n  maroon: 8388608,\n  mediumaquamarine: 6737322,\n  mediumblue: 205,\n  mediumorchid: 12211667,\n  mediumpurple: 9662683,\n  mediumseagreen: 3978097,\n  mediumslateblue: 8087790,\n  mediumspringgreen: 64154,\n  mediumturquoise: 4772300,\n  mediumvioletred: 13047173,\n  midnightblue: 1644912,\n  mintcream: 16121850,\n  mistyrose: 16770273,\n  moccasin: 16770229,\n  navajowhite: 16768685,\n  navy: 128,\n  oldlace: 16643558,\n  olive: 8421376,\n  olivedrab: 7048739,\n  orange: 16753920,\n  orangered: 16729344,\n  orchid: 14315734,\n  palegoldenrod: 15657130,\n  palegreen: 10025880,\n  paleturquoise: 11529966,\n  palevioletred: 14381203,\n  papayawhip: 16773077,\n  peachpuff: 16767673,\n  peru: 13468991,\n  pink: 16761035,\n  plum: 14524637,\n  powderblue: 11591910,\n  purple: 8388736,\n  rebeccapurple: 6697881,\n  red: 16711680,\n  rosybrown: 12357519,\n  royalblue: 4286945,\n  saddlebrown: 9127187,\n  salmon: 16416882,\n  sandybrown: 16032864,\n  seagreen: 3050327,\n  seashell: 16774638,\n  sienna: 10506797,\n  silver: 12632256,\n  skyblue: 8900331,\n  slateblue: 6970061,\n  slategray: 7372944,\n  slategrey: 7372944,\n  snow: 16775930,\n  springgreen: 65407,\n  steelblue: 4620980,\n  tan: 13808780,\n  teal: 32896,\n  thistle: 14204888,\n  tomato: 16737095,\n  turquoise: 4251856,\n  violet: 15631086,\n  wheat: 16113331,\n  white: 16777215,\n  whitesmoke: 16119285,\n  yellow: 16776960,\n  yellowgreen: 10145074\n};\ndefine(Color, color, {\n  copy(channels) {\n    return Object.assign(new this.constructor(), this, channels);\n  },\n  displayable() {\n    return this.rgb().displayable();\n  },\n  hex: color_formatHex,\n  // Deprecated! Use color.formatHex.\n  formatHex: color_formatHex,\n  formatHex8: color_formatHex8,\n  formatHsl: color_formatHsl,\n  formatRgb: color_formatRgb,\n  toString: color_formatRgb\n});\nfunction color_formatHex() {\n  return this.rgb().formatHex();\n}\nfunction color_formatHex8() {\n  return this.rgb().formatHex8();\n}\nfunction color_formatHsl() {\n  return hslConvert(this).formatHsl();\n}\nfunction color_formatRgb() {\n  return this.rgb().formatRgb();\n}\nfunction color(format) {\n  var m, l;\n  format = (format + \"\").trim().toLowerCase();\n  return (m = reHex.exec(format)) ? (l = m[1].length, m = parseInt(m[1], 16), l === 6 ? rgbn(m) : l === 3 ? new Rgb(m >> 8 & 15 | m >> 4 & 240, m >> 4 & 15 | m & 240, (m & 15) << 4 | m & 15, 1) : l === 8 ? rgba(m >> 24 & 255, m >> 16 & 255, m >> 8 & 255, (m & 255) / 255) : l === 4 ? rgba(m >> 12 & 15 | m >> 8 & 240, m >> 8 & 15 | m >> 4 & 240, m >> 4 & 15 | m & 240, ((m & 15) << 4 | m & 15) / 255) : null) : (m = reRgbInteger.exec(format)) ? new Rgb(m[1], m[2], m[3], 1) : (m = reRgbPercent.exec(format)) ? new Rgb(m[1] * 255 / 100, m[2] * 255 / 100, m[3] * 255 / 100, 1) : (m = reRgbaInteger.exec(format)) ? rgba(m[1], m[2], m[3], m[4]) : (m = reRgbaPercent.exec(format)) ? rgba(m[1] * 255 / 100, m[2] * 255 / 100, m[3] * 255 / 100, m[4]) : (m = reHslPercent.exec(format)) ? hsla(m[1], m[2] / 100, m[3] / 100, 1) : (m = reHslaPercent.exec(format)) ? hsla(m[1], m[2] / 100, m[3] / 100, m[4]) : named.hasOwnProperty(format) ? rgbn(named[format]) : format === \"transparent\" ? new Rgb(NaN, NaN, NaN, 0) : null;\n}\nfunction rgbn(n) {\n  return new Rgb(n >> 16 & 255, n >> 8 & 255, n & 255, 1);\n}\nfunction rgba(r, g, b, a) {\n  if (a <= 0) r = g = b = NaN;\n  return new Rgb(r, g, b, a);\n}\nfunction rgbConvert(o) {\n  if (!(o instanceof Color)) o = color(o);\n  if (!o) return new Rgb();\n  o = o.rgb();\n  return new Rgb(o.r, o.g, o.b, o.opacity);\n}\nfunction rgb(r, g, b, opacity) {\n  return arguments.length === 1 ? rgbConvert(r) : new Rgb(r, g, b, opacity == null ? 1 : opacity);\n}\nfunction Rgb(r, g, b, opacity) {\n  this.r = +r;\n  this.g = +g;\n  this.b = +b;\n  this.opacity = +opacity;\n}\ndefine(Rgb, rgb, extend(Color, {\n  brighter(k) {\n    k = k == null ? brighter : Math.pow(brighter, k);\n    return new Rgb(this.r * k, this.g * k, this.b * k, this.opacity);\n  },\n  darker(k) {\n    k = k == null ? darker : Math.pow(darker, k);\n    return new Rgb(this.r * k, this.g * k, this.b * k, this.opacity);\n  },\n  rgb() {\n    return this;\n  },\n  clamp() {\n    return new Rgb(clampi(this.r), clampi(this.g), clampi(this.b), clampa(this.opacity));\n  },\n  displayable() {\n    return -0.5 <= this.r && this.r < 255.5 && (-0.5 <= this.g && this.g < 255.5) && (-0.5 <= this.b && this.b < 255.5) && (0 <= this.opacity && this.opacity <= 1);\n  },\n  hex: rgb_formatHex,\n  // Deprecated! Use color.formatHex.\n  formatHex: rgb_formatHex,\n  formatHex8: rgb_formatHex8,\n  formatRgb: rgb_formatRgb,\n  toString: rgb_formatRgb\n}));\nfunction rgb_formatHex() {\n  return `#${hex(this.r)}${hex(this.g)}${hex(this.b)}`;\n}\nfunction rgb_formatHex8() {\n  return `#${hex(this.r)}${hex(this.g)}${hex(this.b)}${hex((isNaN(this.opacity) ? 1 : this.opacity) * 255)}`;\n}\nfunction rgb_formatRgb() {\n  const a = clampa(this.opacity);\n  return `${a === 1 ? \"rgb(\" : \"rgba(\"}${clampi(this.r)}, ${clampi(this.g)}, ${clampi(this.b)}${a === 1 ? \")\" : `, ${a})`}`;\n}\nfunction clampa(opacity) {\n  return isNaN(opacity) ? 1 : Math.max(0, Math.min(1, opacity));\n}\nfunction clampi(value) {\n  return Math.max(0, Math.min(255, Math.round(value) || 0));\n}\nfunction hex(value) {\n  value = clampi(value);\n  return (value < 16 ? \"0\" : \"\") + value.toString(16);\n}\nfunction hsla(h, s, l, a) {\n  if (a <= 0) h = s = l = NaN;\n  else if (l <= 0 || l >= 1) h = s = NaN;\n  else if (s <= 0) h = NaN;\n  return new Hsl(h, s, l, a);\n}\nfunction hslConvert(o) {\n  if (o instanceof Hsl) return new Hsl(o.h, o.s, o.l, o.opacity);\n  if (!(o instanceof Color)) o = color(o);\n  if (!o) return new Hsl();\n  if (o instanceof Hsl) return o;\n  o = o.rgb();\n  var r = o.r / 255, g = o.g / 255, b = o.b / 255, min = Math.min(r, g, b), max = Math.max(r, g, b), h = NaN, s = max - min, l = (max + min) / 2;\n  if (s) {\n    if (r === max) h = (g - b) / s + (g < b) * 6;\n    else if (g === max) h = (b - r) / s + 2;\n    else h = (r - g) / s + 4;\n    s /= l < 0.5 ? max + min : 2 - max - min;\n    h *= 60;\n  } else {\n    s = l > 0 && l < 1 ? 0 : h;\n  }\n  return new Hsl(h, s, l, o.opacity);\n}\nfunction hsl(h, s, l, opacity) {\n  return arguments.length === 1 ? hslConvert(h) : new Hsl(h, s, l, opacity == null ? 1 : opacity);\n}\nfunction Hsl(h, s, l, opacity) {\n  this.h = +h;\n  this.s = +s;\n  this.l = +l;\n  this.opacity = +opacity;\n}\ndefine(Hsl, hsl, extend(Color, {\n  brighter(k) {\n    k = k == null ? brighter : Math.pow(brighter, k);\n    return new Hsl(this.h, this.s, this.l * k, this.opacity);\n  },\n  darker(k) {\n    k = k == null ? darker : Math.pow(darker, k);\n    return new Hsl(this.h, this.s, this.l * k, this.opacity);\n  },\n  rgb() {\n    var h = this.h % 360 + (this.h < 0) * 360, s = isNaN(h) || isNaN(this.s) ? 0 : this.s, l = this.l, m2 = l + (l < 0.5 ? l : 1 - l) * s, m1 = 2 * l - m2;\n    return new Rgb(\n      hsl2rgb(h >= 240 ? h - 240 : h + 120, m1, m2),\n      hsl2rgb(h, m1, m2),\n      hsl2rgb(h < 120 ? h + 240 : h - 120, m1, m2),\n      this.opacity\n    );\n  },\n  clamp() {\n    return new Hsl(clamph(this.h), clampt(this.s), clampt(this.l), clampa(this.opacity));\n  },\n  displayable() {\n    return (0 <= this.s && this.s <= 1 || isNaN(this.s)) && (0 <= this.l && this.l <= 1) && (0 <= this.opacity && this.opacity <= 1);\n  },\n  formatHsl() {\n    const a = clampa(this.opacity);\n    return `${a === 1 ? \"hsl(\" : \"hsla(\"}${clamph(this.h)}, ${clampt(this.s) * 100}%, ${clampt(this.l) * 100}%${a === 1 ? \")\" : `, ${a})`}`;\n  }\n}));\nfunction clamph(value) {\n  value = (value || 0) % 360;\n  return value < 0 ? value + 360 : value;\n}\nfunction clampt(value) {\n  return Math.max(0, Math.min(1, value || 0));\n}\nfunction hsl2rgb(h, m1, m2) {\n  return (h < 60 ? m1 + (m2 - m1) * h / 60 : h < 180 ? m2 : h < 240 ? m1 + (m2 - m1) * (240 - h) / 60 : m1) * 255;\n}\nconst constant$1 = (x) => () => x;\nfunction linear(a, d) {\n  return function(t) {\n    return a + t * d;\n  };\n}\nfunction exponential(a, b, y) {\n  return a = Math.pow(a, y), b = Math.pow(b, y) - a, y = 1 / y, function(t) {\n    return Math.pow(a + t * b, y);\n  };\n}\nfunction gamma(y) {\n  return (y = +y) === 1 ? nogamma : function(a, b) {\n    return b - a ? exponential(a, b, y) : constant$1(isNaN(a) ? b : a);\n  };\n}\nfunction nogamma(a, b) {\n  var d = b - a;\n  return d ? linear(a, d) : constant$1(isNaN(a) ? b : a);\n}\nconst interpolateRgb = function rgbGamma(y) {\n  var color2 = gamma(y);\n  function rgb$1(start2, end) {\n    var r = color2((start2 = rgb(start2)).r, (end = rgb(end)).r), g = color2(start2.g, end.g), b = color2(start2.b, end.b), opacity = nogamma(start2.opacity, end.opacity);\n    return function(t) {\n      start2.r = r(t);\n      start2.g = g(t);\n      start2.b = b(t);\n      start2.opacity = opacity(t);\n      return start2 + \"\";\n    };\n  }\n  rgb$1.gamma = rgbGamma;\n  return rgb$1;\n}(1);\nfunction interpolateNumber(a, b) {\n  return a = +a, b = +b, function(t) {\n    return a * (1 - t) + b * t;\n  };\n}\nvar reA = /[-+]?(?:\\d+\\.?\\d*|\\.?\\d+)(?:[eE][-+]?\\d+)?/g, reB = new RegExp(reA.source, \"g\");\nfunction zero(b) {\n  return function() {\n    return b;\n  };\n}\nfunction one(b) {\n  return function(t) {\n    return b(t) + \"\";\n  };\n}\nfunction interpolateString(a, b) {\n  var bi = reA.lastIndex = reB.lastIndex = 0, am, bm, bs, i = -1, s = [], q = [];\n  a = a + \"\", b = b + \"\";\n  while ((am = reA.exec(a)) && (bm = reB.exec(b))) {\n    if ((bs = bm.index) > bi) {\n      bs = b.slice(bi, bs);\n      if (s[i]) s[i] += bs;\n      else s[++i] = bs;\n    }\n    if ((am = am[0]) === (bm = bm[0])) {\n      if (s[i]) s[i] += bm;\n      else s[++i] = bm;\n    } else {\n      s[++i] = null;\n      q.push({ i, x: interpolateNumber(am, bm) });\n    }\n    bi = reB.lastIndex;\n  }\n  if (bi < b.length) {\n    bs = b.slice(bi);\n    if (s[i]) s[i] += bs;\n    else s[++i] = bs;\n  }\n  return s.length < 2 ? q[0] ? one(q[0].x) : zero(b) : (b = q.length, function(t) {\n    for (var i2 = 0, o; i2 < b; ++i2) s[(o = q[i2]).i] = o.x(t);\n    return s.join(\"\");\n  });\n}\nvar degrees = 180 / Math.PI;\nvar identity$1 = {\n  translateX: 0,\n  translateY: 0,\n  rotate: 0,\n  skewX: 0,\n  scaleX: 1,\n  scaleY: 1\n};\nfunction decompose(a, b, c, d, e, f) {\n  var scaleX, scaleY, skewX;\n  if (scaleX = Math.sqrt(a * a + b * b)) a /= scaleX, b /= scaleX;\n  if (skewX = a * c + b * d) c -= a * skewX, d -= b * skewX;\n  if (scaleY = Math.sqrt(c * c + d * d)) c /= scaleY, d /= scaleY, skewX /= scaleY;\n  if (a * d < b * c) a = -a, b = -b, skewX = -skewX, scaleX = -scaleX;\n  return {\n    translateX: e,\n    translateY: f,\n    rotate: Math.atan2(b, a) * degrees,\n    skewX: Math.atan(skewX) * degrees,\n    scaleX,\n    scaleY\n  };\n}\nvar svgNode;\nfunction parseCss(value) {\n  const m = new (typeof DOMMatrix === \"function\" ? DOMMatrix : WebKitCSSMatrix)(value + \"\");\n  return m.isIdentity ? identity$1 : decompose(m.a, m.b, m.c, m.d, m.e, m.f);\n}\nfunction parseSvg(value) {\n  if (value == null) return identity$1;\n  if (!svgNode) svgNode = document.createElementNS(\"http://www.w3.org/2000/svg\", \"g\");\n  svgNode.setAttribute(\"transform\", value);\n  if (!(value = svgNode.transform.baseVal.consolidate())) return identity$1;\n  value = value.matrix;\n  return decompose(value.a, value.b, value.c, value.d, value.e, value.f);\n}\nfunction interpolateTransform(parse, pxComma, pxParen, degParen) {\n  function pop(s) {\n    return s.length ? s.pop() + \" \" : \"\";\n  }\n  function translate(xa, ya, xb, yb, s, q) {\n    if (xa !== xb || ya !== yb) {\n      var i = s.push(\"translate(\", null, pxComma, null, pxParen);\n      q.push({ i: i - 4, x: interpolateNumber(xa, xb) }, { i: i - 2, x: interpolateNumber(ya, yb) });\n    } else if (xb || yb) {\n      s.push(\"translate(\" + xb + pxComma + yb + pxParen);\n    }\n  }\n  function rotate(a, b, s, q) {\n    if (a !== b) {\n      if (a - b > 180) b += 360;\n      else if (b - a > 180) a += 360;\n      q.push({ i: s.push(pop(s) + \"rotate(\", null, degParen) - 2, x: interpolateNumber(a, b) });\n    } else if (b) {\n      s.push(pop(s) + \"rotate(\" + b + degParen);\n    }\n  }\n  function skewX(a, b, s, q) {\n    if (a !== b) {\n      q.push({ i: s.push(pop(s) + \"skewX(\", null, degParen) - 2, x: interpolateNumber(a, b) });\n    } else if (b) {\n      s.push(pop(s) + \"skewX(\" + b + degParen);\n    }\n  }\n  function scale(xa, ya, xb, yb, s, q) {\n    if (xa !== xb || ya !== yb) {\n      var i = s.push(pop(s) + \"scale(\", null, \",\", null, \")\");\n      q.push({ i: i - 4, x: interpolateNumber(xa, xb) }, { i: i - 2, x: interpolateNumber(ya, yb) });\n    } else if (xb !== 1 || yb !== 1) {\n      s.push(pop(s) + \"scale(\" + xb + \",\" + yb + \")\");\n    }\n  }\n  return function(a, b) {\n    var s = [], q = [];\n    a = parse(a), b = parse(b);\n    translate(a.translateX, a.translateY, b.translateX, b.translateY, s, q);\n    rotate(a.rotate, b.rotate, s, q);\n    skewX(a.skewX, b.skewX, s, q);\n    scale(a.scaleX, a.scaleY, b.scaleX, b.scaleY, s, q);\n    a = b = null;\n    return function(t) {\n      var i = -1, n = q.length, o;\n      while (++i < n) s[(o = q[i]).i] = o.x(t);\n      return s.join(\"\");\n    };\n  };\n}\nvar interpolateTransformCss = interpolateTransform(parseCss, \"px, \", \"px)\", \"deg)\");\nvar interpolateTransformSvg = interpolateTransform(parseSvg, \", \", \")\", \")\");\nvar epsilon2 = 1e-12;\nfunction cosh(x) {\n  return ((x = Math.exp(x)) + 1 / x) / 2;\n}\nfunction sinh(x) {\n  return ((x = Math.exp(x)) - 1 / x) / 2;\n}\nfunction tanh(x) {\n  return ((x = Math.exp(2 * x)) - 1) / (x + 1);\n}\nconst interpolateZoom = function zoomRho(rho, rho2, rho4) {\n  function zoom2(p0, p1) {\n    var ux0 = p0[0], uy0 = p0[1], w0 = p0[2], ux1 = p1[0], uy1 = p1[1], w1 = p1[2], dx = ux1 - ux0, dy = uy1 - uy0, d2 = dx * dx + dy * dy, i, S;\n    if (d2 < epsilon2) {\n      S = Math.log(w1 / w0) / rho;\n      i = function(t) {\n        return [\n          ux0 + t * dx,\n          uy0 + t * dy,\n          w0 * Math.exp(rho * t * S)\n        ];\n      };\n    } else {\n      var d1 = Math.sqrt(d2), b0 = (w1 * w1 - w0 * w0 + rho4 * d2) / (2 * w0 * rho2 * d1), b1 = (w1 * w1 - w0 * w0 - rho4 * d2) / (2 * w1 * rho2 * d1), r0 = Math.log(Math.sqrt(b0 * b0 + 1) - b0), r1 = Math.log(Math.sqrt(b1 * b1 + 1) - b1);\n      S = (r1 - r0) / rho;\n      i = function(t) {\n        var s = t * S, coshr0 = cosh(r0), u = w0 / (rho2 * d1) * (coshr0 * tanh(rho * s + r0) - sinh(r0));\n        return [\n          ux0 + u * dx,\n          uy0 + u * dy,\n          w0 * coshr0 / cosh(rho * s + r0)\n        ];\n      };\n    }\n    i.duration = S * 1e3 * rho / Math.SQRT2;\n    return i;\n  }\n  zoom2.rho = function(_) {\n    var _1 = Math.max(1e-3, +_), _2 = _1 * _1, _4 = _2 * _2;\n    return zoomRho(_1, _2, _4);\n  };\n  return zoom2;\n}(Math.SQRT2, 2, 4);\nvar frame = 0, timeout$1 = 0, interval = 0, pokeDelay = 1e3, taskHead, taskTail, clockLast = 0, clockNow = 0, clockSkew = 0, clock = typeof performance === \"object\" && performance.now ? performance : Date, setFrame = typeof window === \"object\" && window.requestAnimationFrame ? window.requestAnimationFrame.bind(window) : function(f) {\n  setTimeout(f, 17);\n};\nfunction now() {\n  return clockNow || (setFrame(clearNow), clockNow = clock.now() + clockSkew);\n}\nfunction clearNow() {\n  clockNow = 0;\n}\nfunction Timer() {\n  this._call = this._time = this._next = null;\n}\nTimer.prototype = timer.prototype = {\n  constructor: Timer,\n  restart: function(callback, delay, time) {\n    if (typeof callback !== \"function\") throw new TypeError(\"callback is not a function\");\n    time = (time == null ? now() : +time) + (delay == null ? 0 : +delay);\n    if (!this._next && taskTail !== this) {\n      if (taskTail) taskTail._next = this;\n      else taskHead = this;\n      taskTail = this;\n    }\n    this._call = callback;\n    this._time = time;\n    sleep();\n  },\n  stop: function() {\n    if (this._call) {\n      this._call = null;\n      this._time = Infinity;\n      sleep();\n    }\n  }\n};\nfunction timer(callback, delay, time) {\n  var t = new Timer();\n  t.restart(callback, delay, time);\n  return t;\n}\nfunction timerFlush() {\n  now();\n  ++frame;\n  var t = taskHead, e;\n  while (t) {\n    if ((e = clockNow - t._time) >= 0) t._call.call(void 0, e);\n    t = t._next;\n  }\n  --frame;\n}\nfunction wake() {\n  clockNow = (clockLast = clock.now()) + clockSkew;\n  frame = timeout$1 = 0;\n  try {\n    timerFlush();\n  } finally {\n    frame = 0;\n    nap();\n    clockNow = 0;\n  }\n}\nfunction poke() {\n  var now2 = clock.now(), delay = now2 - clockLast;\n  if (delay > pokeDelay) clockSkew -= delay, clockLast = now2;\n}\nfunction nap() {\n  var t0, t1 = taskHead, t2, time = Infinity;\n  while (t1) {\n    if (t1._call) {\n      if (time > t1._time) time = t1._time;\n      t0 = t1, t1 = t1._next;\n    } else {\n      t2 = t1._next, t1._next = null;\n      t1 = t0 ? t0._next = t2 : taskHead = t2;\n    }\n  }\n  taskTail = t0;\n  sleep(time);\n}\nfunction sleep(time) {\n  if (frame) return;\n  if (timeout$1) timeout$1 = clearTimeout(timeout$1);\n  var delay = time - clockNow;\n  if (delay > 24) {\n    if (time < Infinity) timeout$1 = setTimeout(wake, time - clock.now() - clockSkew);\n    if (interval) interval = clearInterval(interval);\n  } else {\n    if (!interval) clockLast = clock.now(), interval = setInterval(poke, pokeDelay);\n    frame = 1, setFrame(wake);\n  }\n}\nfunction timeout(callback, delay, time) {\n  var t = new Timer();\n  delay = delay == null ? 0 : +delay;\n  t.restart((elapsed) => {\n    t.stop();\n    callback(elapsed + delay);\n  }, delay, time);\n  return t;\n}\nvar emptyOn = dispatch(\"start\", \"end\", \"cancel\", \"interrupt\");\nvar emptyTween = [];\nvar CREATED = 0;\nvar SCHEDULED = 1;\nvar STARTING = 2;\nvar STARTED = 3;\nvar RUNNING = 4;\nvar ENDING = 5;\nvar ENDED = 6;\nfunction schedule(node, name, id2, index, group, timing) {\n  var schedules = node.__transition;\n  if (!schedules) node.__transition = {};\n  else if (id2 in schedules) return;\n  create(node, id2, {\n    name,\n    index,\n    // For context during callback.\n    group,\n    // For context during callback.\n    on: emptyOn,\n    tween: emptyTween,\n    time: timing.time,\n    delay: timing.delay,\n    duration: timing.duration,\n    ease: timing.ease,\n    timer: null,\n    state: CREATED\n  });\n}\nfunction init(node, id2) {\n  var schedule2 = get(node, id2);\n  if (schedule2.state > CREATED) throw new Error(\"too late; already scheduled\");\n  return schedule2;\n}\nfunction set(node, id2) {\n  var schedule2 = get(node, id2);\n  if (schedule2.state > STARTED) throw new Error(\"too late; already running\");\n  return schedule2;\n}\nfunction get(node, id2) {\n  var schedule2 = node.__transition;\n  if (!schedule2 || !(schedule2 = schedule2[id2])) throw new Error(\"transition not found\");\n  return schedule2;\n}\nfunction create(node, id2, self) {\n  var schedules = node.__transition, tween;\n  schedules[id2] = self;\n  self.timer = timer(schedule2, 0, self.time);\n  function schedule2(elapsed) {\n    self.state = SCHEDULED;\n    self.timer.restart(start2, self.delay, self.time);\n    if (self.delay <= elapsed) start2(elapsed - self.delay);\n  }\n  function start2(elapsed) {\n    var i, j, n, o;\n    if (self.state !== SCHEDULED) return stop();\n    for (i in schedules) {\n      o = schedules[i];\n      if (o.name !== self.name) continue;\n      if (o.state === STARTED) return timeout(start2);\n      if (o.state === RUNNING) {\n        o.state = ENDED;\n        o.timer.stop();\n        o.on.call(\"interrupt\", node, node.__data__, o.index, o.group);\n        delete schedules[i];\n      } else if (+i < id2) {\n        o.state = ENDED;\n        o.timer.stop();\n        o.on.call(\"cancel\", node, node.__data__, o.index, o.group);\n        delete schedules[i];\n      }\n    }\n    timeout(function() {\n      if (self.state === STARTED) {\n        self.state = RUNNING;\n        self.timer.restart(tick, self.delay, self.time);\n        tick(elapsed);\n      }\n    });\n    self.state = STARTING;\n    self.on.call(\"start\", node, node.__data__, self.index, self.group);\n    if (self.state !== STARTING) return;\n    self.state = STARTED;\n    tween = new Array(n = self.tween.length);\n    for (i = 0, j = -1; i < n; ++i) {\n      if (o = self.tween[i].value.call(node, node.__data__, self.index, self.group)) {\n        tween[++j] = o;\n      }\n    }\n    tween.length = j + 1;\n  }\n  function tick(elapsed) {\n    var t = elapsed < self.duration ? self.ease.call(null, elapsed / self.duration) : (self.timer.restart(stop), self.state = ENDING, 1), i = -1, n = tween.length;\n    while (++i < n) {\n      tween[i].call(node, t);\n    }\n    if (self.state === ENDING) {\n      self.on.call(\"end\", node, node.__data__, self.index, self.group);\n      stop();\n    }\n  }\n  function stop() {\n    self.state = ENDED;\n    self.timer.stop();\n    delete schedules[id2];\n    for (var i in schedules) return;\n    delete node.__transition;\n  }\n}\nfunction interrupt(node, name) {\n  var schedules = node.__transition, schedule2, active, empty2 = true, i;\n  if (!schedules) return;\n  name = name == null ? null : name + \"\";\n  for (i in schedules) {\n    if ((schedule2 = schedules[i]).name !== name) {\n      empty2 = false;\n      continue;\n    }\n    active = schedule2.state > STARTING && schedule2.state < ENDING;\n    schedule2.state = ENDED;\n    schedule2.timer.stop();\n    schedule2.on.call(active ? \"interrupt\" : \"cancel\", node, node.__data__, schedule2.index, schedule2.group);\n    delete schedules[i];\n  }\n  if (empty2) delete node.__transition;\n}\nfunction selection_interrupt(name) {\n  return this.each(function() {\n    interrupt(this, name);\n  });\n}\nfunction tweenRemove(id2, name) {\n  var tween0, tween1;\n  return function() {\n    var schedule2 = set(this, id2), tween = schedule2.tween;\n    if (tween !== tween0) {\n      tween1 = tween0 = tween;\n      for (var i = 0, n = tween1.length; i < n; ++i) {\n        if (tween1[i].name === name) {\n          tween1 = tween1.slice();\n          tween1.splice(i, 1);\n          break;\n        }\n      }\n    }\n    schedule2.tween = tween1;\n  };\n}\nfunction tweenFunction(id2, name, value) {\n  var tween0, tween1;\n  if (typeof value !== \"function\") throw new Error();\n  return function() {\n    var schedule2 = set(this, id2), tween = schedule2.tween;\n    if (tween !== tween0) {\n      tween1 = (tween0 = tween).slice();\n      for (var t = { name, value }, i = 0, n = tween1.length; i < n; ++i) {\n        if (tween1[i].name === name) {\n          tween1[i] = t;\n          break;\n        }\n      }\n      if (i === n) tween1.push(t);\n    }\n    schedule2.tween = tween1;\n  };\n}\nfunction transition_tween(name, value) {\n  var id2 = this._id;\n  name += \"\";\n  if (arguments.length < 2) {\n    var tween = get(this.node(), id2).tween;\n    for (var i = 0, n = tween.length, t; i < n; ++i) {\n      if ((t = tween[i]).name === name) {\n        return t.value;\n      }\n    }\n    return null;\n  }\n  return this.each((value == null ? tweenRemove : tweenFunction)(id2, name, value));\n}\nfunction tweenValue(transition, name, value) {\n  var id2 = transition._id;\n  transition.each(function() {\n    var schedule2 = set(this, id2);\n    (schedule2.value || (schedule2.value = {}))[name] = value.apply(this, arguments);\n  });\n  return function(node) {\n    return get(node, id2).value[name];\n  };\n}\nfunction interpolate(a, b) {\n  var c;\n  return (typeof b === \"number\" ? interpolateNumber : b instanceof color ? interpolateRgb : (c = color(b)) ? (b = c, interpolateRgb) : interpolateString)(a, b);\n}\nfunction attrRemove(name) {\n  return function() {\n    this.removeAttribute(name);\n  };\n}\nfunction attrRemoveNS(fullname) {\n  return function() {\n    this.removeAttributeNS(fullname.space, fullname.local);\n  };\n}\nfunction attrConstant(name, interpolate2, value1) {\n  var string00, string1 = value1 + \"\", interpolate0;\n  return function() {\n    var string0 = this.getAttribute(name);\n    return string0 === string1 ? null : string0 === string00 ? interpolate0 : interpolate0 = interpolate2(string00 = string0, value1);\n  };\n}\nfunction attrConstantNS(fullname, interpolate2, value1) {\n  var string00, string1 = value1 + \"\", interpolate0;\n  return function() {\n    var string0 = this.getAttributeNS(fullname.space, fullname.local);\n    return string0 === string1 ? null : string0 === string00 ? interpolate0 : interpolate0 = interpolate2(string00 = string0, value1);\n  };\n}\nfunction attrFunction(name, interpolate2, value) {\n  var string00, string10, interpolate0;\n  return function() {\n    var string0, value1 = value(this), string1;\n    if (value1 == null) return void this.removeAttribute(name);\n    string0 = this.getAttribute(name);\n    string1 = value1 + \"\";\n    return string0 === string1 ? null : string0 === string00 && string1 === string10 ? interpolate0 : (string10 = string1, interpolate0 = interpolate2(string00 = string0, value1));\n  };\n}\nfunction attrFunctionNS(fullname, interpolate2, value) {\n  var string00, string10, interpolate0;\n  return function() {\n    var string0, value1 = value(this), string1;\n    if (value1 == null) return void this.removeAttributeNS(fullname.space, fullname.local);\n    string0 = this.getAttributeNS(fullname.space, fullname.local);\n    string1 = value1 + \"\";\n    return string0 === string1 ? null : string0 === string00 && string1 === string10 ? interpolate0 : (string10 = string1, interpolate0 = interpolate2(string00 = string0, value1));\n  };\n}\nfunction transition_attr(name, value) {\n  var fullname = namespace(name), i = fullname === \"transform\" ? interpolateTransformSvg : interpolate;\n  return this.attrTween(name, typeof value === \"function\" ? (fullname.local ? attrFunctionNS : attrFunction)(fullname, i, tweenValue(this, \"attr.\" + name, value)) : value == null ? (fullname.local ? attrRemoveNS : attrRemove)(fullname) : (fullname.local ? attrConstantNS : attrConstant)(fullname, i, value));\n}\nfunction attrInterpolate(name, i) {\n  return function(t) {\n    this.setAttribute(name, i.call(this, t));\n  };\n}\nfunction attrInterpolateNS(fullname, i) {\n  return function(t) {\n    this.setAttributeNS(fullname.space, fullname.local, i.call(this, t));\n  };\n}\nfunction attrTweenNS(fullname, value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && attrInterpolateNS(fullname, i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\nfunction attrTween(name, value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && attrInterpolate(name, i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\nfunction transition_attrTween(name, value) {\n  var key = \"attr.\" + name;\n  if (arguments.length < 2) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error();\n  var fullname = namespace(name);\n  return this.tween(key, (fullname.local ? attrTweenNS : attrTween)(fullname, value));\n}\nfunction delayFunction(id2, value) {\n  return function() {\n    init(this, id2).delay = +value.apply(this, arguments);\n  };\n}\nfunction delayConstant(id2, value) {\n  return value = +value, function() {\n    init(this, id2).delay = value;\n  };\n}\nfunction transition_delay(value) {\n  var id2 = this._id;\n  return arguments.length ? this.each((typeof value === \"function\" ? delayFunction : delayConstant)(id2, value)) : get(this.node(), id2).delay;\n}\nfunction durationFunction(id2, value) {\n  return function() {\n    set(this, id2).duration = +value.apply(this, arguments);\n  };\n}\nfunction durationConstant(id2, value) {\n  return value = +value, function() {\n    set(this, id2).duration = value;\n  };\n}\nfunction transition_duration(value) {\n  var id2 = this._id;\n  return arguments.length ? this.each((typeof value === \"function\" ? durationFunction : durationConstant)(id2, value)) : get(this.node(), id2).duration;\n}\nfunction easeConstant(id2, value) {\n  if (typeof value !== \"function\") throw new Error();\n  return function() {\n    set(this, id2).ease = value;\n  };\n}\nfunction transition_ease(value) {\n  var id2 = this._id;\n  return arguments.length ? this.each(easeConstant(id2, value)) : get(this.node(), id2).ease;\n}\nfunction easeVarying(id2, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (typeof v !== \"function\") throw new Error();\n    set(this, id2).ease = v;\n  };\n}\nfunction transition_easeVarying(value) {\n  if (typeof value !== \"function\") throw new Error();\n  return this.each(easeVarying(this._id, value));\n}\nfunction transition_filter(match) {\n  if (typeof match !== \"function\") match = matcher(match);\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = [], node, i = 0; i < n; ++i) {\n      if ((node = group[i]) && match.call(node, node.__data__, i, group)) {\n        subgroup.push(node);\n      }\n    }\n  }\n  return new Transition(subgroups, this._parents, this._name, this._id);\n}\nfunction transition_merge(transition) {\n  if (transition._id !== this._id) throw new Error();\n  for (var groups0 = this._groups, groups1 = transition._groups, m0 = groups0.length, m1 = groups1.length, m = Math.min(m0, m1), merges = new Array(m0), j = 0; j < m; ++j) {\n    for (var group0 = groups0[j], group1 = groups1[j], n = group0.length, merge = merges[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group0[i] || group1[i]) {\n        merge[i] = node;\n      }\n    }\n  }\n  for (; j < m0; ++j) {\n    merges[j] = groups0[j];\n  }\n  return new Transition(merges, this._parents, this._name, this._id);\n}\nfunction start(name) {\n  return (name + \"\").trim().split(/^|\\s+/).every(function(t) {\n    var i = t.indexOf(\".\");\n    if (i >= 0) t = t.slice(0, i);\n    return !t || t === \"start\";\n  });\n}\nfunction onFunction(id2, name, listener) {\n  var on0, on1, sit = start(name) ? init : set;\n  return function() {\n    var schedule2 = sit(this, id2), on = schedule2.on;\n    if (on !== on0) (on1 = (on0 = on).copy()).on(name, listener);\n    schedule2.on = on1;\n  };\n}\nfunction transition_on(name, listener) {\n  var id2 = this._id;\n  return arguments.length < 2 ? get(this.node(), id2).on.on(name) : this.each(onFunction(id2, name, listener));\n}\nfunction removeFunction(id2) {\n  return function() {\n    var parent = this.parentNode;\n    for (var i in this.__transition) if (+i !== id2) return;\n    if (parent) parent.removeChild(this);\n  };\n}\nfunction transition_remove() {\n  return this.on(\"end.remove\", removeFunction(this._id));\n}\nfunction transition_select(select2) {\n  var name = this._name, id2 = this._id;\n  if (typeof select2 !== \"function\") select2 = selector(select2);\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = new Array(n), node, subnode, i = 0; i < n; ++i) {\n      if ((node = group[i]) && (subnode = select2.call(node, node.__data__, i, group))) {\n        if (\"__data__\" in node) subnode.__data__ = node.__data__;\n        subgroup[i] = subnode;\n        schedule(subgroup[i], name, id2, i, subgroup, get(node, id2));\n      }\n    }\n  }\n  return new Transition(subgroups, this._parents, name, id2);\n}\nfunction transition_selectAll(select2) {\n  var name = this._name, id2 = this._id;\n  if (typeof select2 !== \"function\") select2 = selectorAll(select2);\n  for (var groups = this._groups, m = groups.length, subgroups = [], parents = [], j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        for (var children2 = select2.call(node, node.__data__, i, group), child, inherit2 = get(node, id2), k = 0, l = children2.length; k < l; ++k) {\n          if (child = children2[k]) {\n            schedule(child, name, id2, k, children2, inherit2);\n          }\n        }\n        subgroups.push(children2);\n        parents.push(node);\n      }\n    }\n  }\n  return new Transition(subgroups, parents, name, id2);\n}\nvar Selection = selection.prototype.constructor;\nfunction transition_selection() {\n  return new Selection(this._groups, this._parents);\n}\nfunction styleNull(name, interpolate2) {\n  var string00, string10, interpolate0;\n  return function() {\n    var string0 = styleValue(this, name), string1 = (this.style.removeProperty(name), styleValue(this, name));\n    return string0 === string1 ? null : string0 === string00 && string1 === string10 ? interpolate0 : interpolate0 = interpolate2(string00 = string0, string10 = string1);\n  };\n}\nfunction styleRemove(name) {\n  return function() {\n    this.style.removeProperty(name);\n  };\n}\nfunction styleConstant(name, interpolate2, value1) {\n  var string00, string1 = value1 + \"\", interpolate0;\n  return function() {\n    var string0 = styleValue(this, name);\n    return string0 === string1 ? null : string0 === string00 ? interpolate0 : interpolate0 = interpolate2(string00 = string0, value1);\n  };\n}\nfunction styleFunction(name, interpolate2, value) {\n  var string00, string10, interpolate0;\n  return function() {\n    var string0 = styleValue(this, name), value1 = value(this), string1 = value1 + \"\";\n    if (value1 == null) string1 = value1 = (this.style.removeProperty(name), styleValue(this, name));\n    return string0 === string1 ? null : string0 === string00 && string1 === string10 ? interpolate0 : (string10 = string1, interpolate0 = interpolate2(string00 = string0, value1));\n  };\n}\nfunction styleMaybeRemove(id2, name) {\n  var on0, on1, listener0, key = \"style.\" + name, event = \"end.\" + key, remove2;\n  return function() {\n    var schedule2 = set(this, id2), on = schedule2.on, listener = schedule2.value[key] == null ? remove2 || (remove2 = styleRemove(name)) : void 0;\n    if (on !== on0 || listener0 !== listener) (on1 = (on0 = on).copy()).on(event, listener0 = listener);\n    schedule2.on = on1;\n  };\n}\nfunction transition_style(name, value, priority) {\n  var i = (name += \"\") === \"transform\" ? interpolateTransformCss : interpolate;\n  return value == null ? this.styleTween(name, styleNull(name, i)).on(\"end.style.\" + name, styleRemove(name)) : typeof value === \"function\" ? this.styleTween(name, styleFunction(name, i, tweenValue(this, \"style.\" + name, value))).each(styleMaybeRemove(this._id, name)) : this.styleTween(name, styleConstant(name, i, value), priority).on(\"end.style.\" + name, null);\n}\nfunction styleInterpolate(name, i, priority) {\n  return function(t) {\n    this.style.setProperty(name, i.call(this, t), priority);\n  };\n}\nfunction styleTween(name, value, priority) {\n  var t, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t = (i0 = i) && styleInterpolate(name, i, priority);\n    return t;\n  }\n  tween._value = value;\n  return tween;\n}\nfunction transition_styleTween(name, value, priority) {\n  var key = \"style.\" + (name += \"\");\n  if (arguments.length < 2) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error();\n  return this.tween(key, styleTween(name, value, priority == null ? \"\" : priority));\n}\nfunction textConstant(value) {\n  return function() {\n    this.textContent = value;\n  };\n}\nfunction textFunction(value) {\n  return function() {\n    var value1 = value(this);\n    this.textContent = value1 == null ? \"\" : value1;\n  };\n}\nfunction transition_text(value) {\n  return this.tween(\"text\", typeof value === \"function\" ? textFunction(tweenValue(this, \"text\", value)) : textConstant(value == null ? \"\" : value + \"\"));\n}\nfunction textInterpolate(i) {\n  return function(t) {\n    this.textContent = i.call(this, t);\n  };\n}\nfunction textTween(value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && textInterpolate(i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\nfunction transition_textTween(value) {\n  var key = \"text\";\n  if (arguments.length < 1) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error();\n  return this.tween(key, textTween(value));\n}\nfunction transition_transition() {\n  var name = this._name, id0 = this._id, id1 = newId();\n  for (var groups = this._groups, m = groups.length, j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        var inherit2 = get(node, id0);\n        schedule(node, name, id1, i, group, {\n          time: inherit2.time + inherit2.delay + inherit2.duration,\n          delay: 0,\n          duration: inherit2.duration,\n          ease: inherit2.ease\n        });\n      }\n    }\n  }\n  return new Transition(groups, this._parents, name, id1);\n}\nfunction transition_end() {\n  var on0, on1, that = this, id2 = that._id, size = that.size();\n  return new Promise(function(resolve, reject) {\n    var cancel = { value: reject }, end = { value: function() {\n      if (--size === 0) resolve();\n    } };\n    that.each(function() {\n      var schedule2 = set(this, id2), on = schedule2.on;\n      if (on !== on0) {\n        on1 = (on0 = on).copy();\n        on1._.cancel.push(cancel);\n        on1._.interrupt.push(cancel);\n        on1._.end.push(end);\n      }\n      schedule2.on = on1;\n    });\n    if (size === 0) resolve();\n  });\n}\nvar id = 0;\nfunction Transition(groups, parents, name, id2) {\n  this._groups = groups;\n  this._parents = parents;\n  this._name = name;\n  this._id = id2;\n}\nfunction newId() {\n  return ++id;\n}\nvar selection_prototype = selection.prototype;\nTransition.prototype = {\n  constructor: Transition,\n  select: transition_select,\n  selectAll: transition_selectAll,\n  selectChild: selection_prototype.selectChild,\n  selectChildren: selection_prototype.selectChildren,\n  filter: transition_filter,\n  merge: transition_merge,\n  selection: transition_selection,\n  transition: transition_transition,\n  call: selection_prototype.call,\n  nodes: selection_prototype.nodes,\n  node: selection_prototype.node,\n  size: selection_prototype.size,\n  empty: selection_prototype.empty,\n  each: selection_prototype.each,\n  on: transition_on,\n  attr: transition_attr,\n  attrTween: transition_attrTween,\n  style: transition_style,\n  styleTween: transition_styleTween,\n  text: transition_text,\n  textTween: transition_textTween,\n  remove: transition_remove,\n  tween: transition_tween,\n  delay: transition_delay,\n  duration: transition_duration,\n  ease: transition_ease,\n  easeVarying: transition_easeVarying,\n  end: transition_end,\n  [Symbol.iterator]: selection_prototype[Symbol.iterator]\n};\nfunction cubicInOut(t) {\n  return ((t *= 2) <= 1 ? t * t * t : (t -= 2) * t * t + 2) / 2;\n}\nvar defaultTiming = {\n  time: null,\n  // Set on use.\n  delay: 0,\n  duration: 250,\n  ease: cubicInOut\n};\nfunction inherit(node, id2) {\n  var timing;\n  while (!(timing = node.__transition) || !(timing = timing[id2])) {\n    if (!(node = node.parentNode)) {\n      throw new Error(`transition ${id2} not found`);\n    }\n  }\n  return timing;\n}\nfunction selection_transition(name) {\n  var id2, timing;\n  if (name instanceof Transition) {\n    id2 = name._id, name = name._name;\n  } else {\n    id2 = newId(), (timing = defaultTiming).time = now(), name = name == null ? null : name + \"\";\n  }\n  for (var groups = this._groups, m = groups.length, j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        schedule(node, name, id2, i, group, timing || inherit(node, id2));\n      }\n    }\n  }\n  return new Transition(groups, this._parents, name, id2);\n}\nselection.prototype.interrupt = selection_interrupt;\nselection.prototype.transition = selection_transition;\nconst constant = (x) => () => x;\nfunction ZoomEvent(type, {\n  sourceEvent: sourceEvent2,\n  target,\n  transform,\n  dispatch: dispatch2\n}) {\n  Object.defineProperties(this, {\n    type: { value: type, enumerable: true, configurable: true },\n    sourceEvent: { value: sourceEvent2, enumerable: true, configurable: true },\n    target: { value: target, enumerable: true, configurable: true },\n    transform: { value: transform, enumerable: true, configurable: true },\n    _: { value: dispatch2 }\n  });\n}\nfunction Transform(k, x, y) {\n  this.k = k;\n  this.x = x;\n  this.y = y;\n}\nTransform.prototype = {\n  constructor: Transform,\n  scale: function(k) {\n    return k === 1 ? this : new Transform(this.k * k, this.x, this.y);\n  },\n  translate: function(x, y) {\n    return x === 0 & y === 0 ? this : new Transform(this.k, this.x + this.k * x, this.y + this.k * y);\n  },\n  apply: function(point) {\n    return [point[0] * this.k + this.x, point[1] * this.k + this.y];\n  },\n  applyX: function(x) {\n    return x * this.k + this.x;\n  },\n  applyY: function(y) {\n    return y * this.k + this.y;\n  },\n  invert: function(location) {\n    return [(location[0] - this.x) / this.k, (location[1] - this.y) / this.k];\n  },\n  invertX: function(x) {\n    return (x - this.x) / this.k;\n  },\n  invertY: function(y) {\n    return (y - this.y) / this.k;\n  },\n  rescaleX: function(x) {\n    return x.copy().domain(x.range().map(this.invertX, this).map(x.invert, x));\n  },\n  rescaleY: function(y) {\n    return y.copy().domain(y.range().map(this.invertY, this).map(y.invert, y));\n  },\n  toString: function() {\n    return \"translate(\" + this.x + \",\" + this.y + \") scale(\" + this.k + \")\";\n  }\n};\nvar identity = new Transform(1, 0, 0);\nTransform.prototype;\nfunction nopropagation(event) {\n  event.stopImmediatePropagation();\n}\nfunction noevent(event) {\n  event.preventDefault();\n  event.stopImmediatePropagation();\n}\nfunction defaultFilter(event) {\n  return (!event.ctrlKey || event.type === \"wheel\") && !event.button;\n}\nfunction defaultExtent() {\n  var e = this;\n  if (e instanceof SVGElement) {\n    e = e.ownerSVGElement || e;\n    if (e.hasAttribute(\"viewBox\")) {\n      e = e.viewBox.baseVal;\n      return [[e.x, e.y], [e.x + e.width, e.y + e.height]];\n    }\n    return [[0, 0], [e.width.baseVal.value, e.height.baseVal.value]];\n  }\n  return [[0, 0], [e.clientWidth, e.clientHeight]];\n}\nfunction defaultTransform() {\n  return this.__zoom || identity;\n}\nfunction defaultWheelDelta(event) {\n  return -event.deltaY * (event.deltaMode === 1 ? 0.05 : event.deltaMode ? 1 : 2e-3) * (event.ctrlKey ? 10 : 1);\n}\nfunction defaultTouchable() {\n  return navigator.maxTouchPoints || \"ontouchstart\" in this;\n}\nfunction defaultConstrain(transform, extent, translateExtent) {\n  var dx0 = transform.invertX(extent[0][0]) - translateExtent[0][0], dx1 = transform.invertX(extent[1][0]) - translateExtent[1][0], dy0 = transform.invertY(extent[0][1]) - translateExtent[0][1], dy1 = transform.invertY(extent[1][1]) - translateExtent[1][1];\n  return transform.translate(\n    dx1 > dx0 ? (dx0 + dx1) / 2 : Math.min(0, dx0) || Math.max(0, dx1),\n    dy1 > dy0 ? (dy0 + dy1) / 2 : Math.min(0, dy0) || Math.max(0, dy1)\n  );\n}\nfunction zoom() {\n  var filter2 = defaultFilter, extent = defaultExtent, constrain = defaultConstrain, wheelDelta2 = defaultWheelDelta, touchable = defaultTouchable, scaleExtent = [0, Infinity], translateExtent = [[-Infinity, -Infinity], [Infinity, Infinity]], duration = 250, interpolate2 = interpolateZoom, listeners = dispatch(\"start\", \"zoom\", \"end\"), touchstarting, touchfirst, touchending, touchDelay = 500, wheelDelay = 150, clickDistance2 = 0, tapDistance = 10;\n  function zoom2(selection2) {\n    selection2.property(\"__zoom\", defaultTransform).on(\"wheel.zoom\", wheeled, { passive: false }).on(\"mousedown.zoom\", mousedowned).on(\"dblclick.zoom\", dblclicked).filter(touchable).on(\"touchstart.zoom\", touchstarted).on(\"touchmove.zoom\", touchmoved).on(\"touchend.zoom touchcancel.zoom\", touchended).style(\"-webkit-tap-highlight-color\", \"rgba(0,0,0,0)\");\n  }\n  zoom2.transform = function(collection, transform, point, event) {\n    var selection2 = collection.selection ? collection.selection() : collection;\n    selection2.property(\"__zoom\", defaultTransform);\n    if (collection !== selection2) {\n      schedule2(collection, transform, point, event);\n    } else {\n      selection2.interrupt().each(function() {\n        gesture(this, arguments).event(event).start().zoom(null, typeof transform === \"function\" ? transform.apply(this, arguments) : transform).end();\n      });\n    }\n  };\n  zoom2.scaleBy = function(selection2, k, p, event) {\n    zoom2.scaleTo(selection2, function() {\n      var k0 = this.__zoom.k, k1 = typeof k === \"function\" ? k.apply(this, arguments) : k;\n      return k0 * k1;\n    }, p, event);\n  };\n  zoom2.scaleTo = function(selection2, k, p, event) {\n    zoom2.transform(selection2, function() {\n      var e = extent.apply(this, arguments), t0 = this.__zoom, p0 = p == null ? centroid(e) : typeof p === \"function\" ? p.apply(this, arguments) : p, p1 = t0.invert(p0), k1 = typeof k === \"function\" ? k.apply(this, arguments) : k;\n      return constrain(translate(scale(t0, k1), p0, p1), e, translateExtent);\n    }, p, event);\n  };\n  zoom2.translateBy = function(selection2, x, y, event) {\n    zoom2.transform(selection2, function() {\n      return constrain(this.__zoom.translate(\n        typeof x === \"function\" ? x.apply(this, arguments) : x,\n        typeof y === \"function\" ? y.apply(this, arguments) : y\n      ), extent.apply(this, arguments), translateExtent);\n    }, null, event);\n  };\n  zoom2.translateTo = function(selection2, x, y, p, event) {\n    zoom2.transform(selection2, function() {\n      var e = extent.apply(this, arguments), t = this.__zoom, p0 = p == null ? centroid(e) : typeof p === \"function\" ? p.apply(this, arguments) : p;\n      return constrain(identity.translate(p0[0], p0[1]).scale(t.k).translate(\n        typeof x === \"function\" ? -x.apply(this, arguments) : -x,\n        typeof y === \"function\" ? -y.apply(this, arguments) : -y\n      ), e, translateExtent);\n    }, p, event);\n  };\n  function scale(transform, k) {\n    k = Math.max(scaleExtent[0], Math.min(scaleExtent[1], k));\n    return k === transform.k ? transform : new Transform(k, transform.x, transform.y);\n  }\n  function translate(transform, p0, p1) {\n    var x = p0[0] - p1[0] * transform.k, y = p0[1] - p1[1] * transform.k;\n    return x === transform.x && y === transform.y ? transform : new Transform(transform.k, x, y);\n  }\n  function centroid(extent2) {\n    return [(+extent2[0][0] + +extent2[1][0]) / 2, (+extent2[0][1] + +extent2[1][1]) / 2];\n  }\n  function schedule2(transition, transform, point, event) {\n    transition.on(\"start.zoom\", function() {\n      gesture(this, arguments).event(event).start();\n    }).on(\"interrupt.zoom end.zoom\", function() {\n      gesture(this, arguments).event(event).end();\n    }).tween(\"zoom\", function() {\n      var that = this, args = arguments, g = gesture(that, args).event(event), e = extent.apply(that, args), p = point == null ? centroid(e) : typeof point === \"function\" ? point.apply(that, args) : point, w = Math.max(e[1][0] - e[0][0], e[1][1] - e[0][1]), a = that.__zoom, b = typeof transform === \"function\" ? transform.apply(that, args) : transform, i = interpolate2(a.invert(p).concat(w / a.k), b.invert(p).concat(w / b.k));\n      return function(t) {\n        if (t === 1) t = b;\n        else {\n          var l = i(t), k = w / l[2];\n          t = new Transform(k, p[0] - l[0] * k, p[1] - l[1] * k);\n        }\n        g.zoom(null, t);\n      };\n    });\n  }\n  function gesture(that, args, clean) {\n    return !clean && that.__zooming || new Gesture(that, args);\n  }\n  function Gesture(that, args) {\n    this.that = that;\n    this.args = args;\n    this.active = 0;\n    this.sourceEvent = null;\n    this.extent = extent.apply(that, args);\n    this.taps = 0;\n  }\n  Gesture.prototype = {\n    event: function(event) {\n      if (event) this.sourceEvent = event;\n      return this;\n    },\n    start: function() {\n      if (++this.active === 1) {\n        this.that.__zooming = this;\n        this.emit(\"start\");\n      }\n      return this;\n    },\n    zoom: function(key, transform) {\n      if (this.mouse && key !== \"mouse\") this.mouse[1] = transform.invert(this.mouse[0]);\n      if (this.touch0 && key !== \"touch\") this.touch0[1] = transform.invert(this.touch0[0]);\n      if (this.touch1 && key !== \"touch\") this.touch1[1] = transform.invert(this.touch1[0]);\n      this.that.__zoom = transform;\n      this.emit(\"zoom\");\n      return this;\n    },\n    end: function() {\n      if (--this.active === 0) {\n        delete this.that.__zooming;\n        this.emit(\"end\");\n      }\n      return this;\n    },\n    emit: function(type) {\n      var d = select(this.that).datum();\n      listeners.call(\n        type,\n        this.that,\n        new ZoomEvent(type, {\n          sourceEvent: this.sourceEvent,\n          target: zoom2,\n          type,\n          transform: this.that.__zoom,\n          dispatch: listeners\n        }),\n        d\n      );\n    }\n  };\n  function wheeled(event, ...args) {\n    if (!filter2.apply(this, arguments)) return;\n    var g = gesture(this, args).event(event), t = this.__zoom, k = Math.max(scaleExtent[0], Math.min(scaleExtent[1], t.k * Math.pow(2, wheelDelta2.apply(this, arguments)))), p = pointer(event);\n    if (g.wheel) {\n      if (g.mouse[0][0] !== p[0] || g.mouse[0][1] !== p[1]) {\n        g.mouse[1] = t.invert(g.mouse[0] = p);\n      }\n      clearTimeout(g.wheel);\n    } else if (t.k === k) return;\n    else {\n      g.mouse = [p, t.invert(p)];\n      interrupt(this);\n      g.start();\n    }\n    noevent(event);\n    g.wheel = setTimeout(wheelidled, wheelDelay);\n    g.zoom(\"mouse\", constrain(translate(scale(t, k), g.mouse[0], g.mouse[1]), g.extent, translateExtent));\n    function wheelidled() {\n      g.wheel = null;\n      g.end();\n    }\n  }\n  function mousedowned(event, ...args) {\n    if (touchending || !filter2.apply(this, arguments)) return;\n    var currentTarget = event.currentTarget, g = gesture(this, args, true).event(event), v = select(event.view).on(\"mousemove.zoom\", mousemoved, true).on(\"mouseup.zoom\", mouseupped, true), p = pointer(event, currentTarget), x0 = event.clientX, y0 = event.clientY;\n    dragDisable(event.view);\n    nopropagation(event);\n    g.mouse = [p, this.__zoom.invert(p)];\n    interrupt(this);\n    g.start();\n    function mousemoved(event2) {\n      noevent(event2);\n      if (!g.moved) {\n        var dx = event2.clientX - x0, dy = event2.clientY - y0;\n        g.moved = dx * dx + dy * dy > clickDistance2;\n      }\n      g.event(event2).zoom(\"mouse\", constrain(translate(g.that.__zoom, g.mouse[0] = pointer(event2, currentTarget), g.mouse[1]), g.extent, translateExtent));\n    }\n    function mouseupped(event2) {\n      v.on(\"mousemove.zoom mouseup.zoom\", null);\n      yesdrag(event2.view, g.moved);\n      noevent(event2);\n      g.event(event2).end();\n    }\n  }\n  function dblclicked(event, ...args) {\n    if (!filter2.apply(this, arguments)) return;\n    var t0 = this.__zoom, p0 = pointer(event.changedTouches ? event.changedTouches[0] : event, this), p1 = t0.invert(p0), k1 = t0.k * (event.shiftKey ? 0.5 : 2), t1 = constrain(translate(scale(t0, k1), p0, p1), extent.apply(this, args), translateExtent);\n    noevent(event);\n    if (duration > 0) select(this).transition().duration(duration).call(schedule2, t1, p0, event);\n    else select(this).call(zoom2.transform, t1, p0, event);\n  }\n  function touchstarted(event, ...args) {\n    if (!filter2.apply(this, arguments)) return;\n    var touches = event.touches, n = touches.length, g = gesture(this, args, event.changedTouches.length === n).event(event), started, i, t, p;\n    nopropagation(event);\n    for (i = 0; i < n; ++i) {\n      t = touches[i], p = pointer(t, this);\n      p = [p, this.__zoom.invert(p), t.identifier];\n      if (!g.touch0) g.touch0 = p, started = true, g.taps = 1 + !!touchstarting;\n      else if (!g.touch1 && g.touch0[2] !== p[2]) g.touch1 = p, g.taps = 0;\n    }\n    if (touchstarting) touchstarting = clearTimeout(touchstarting);\n    if (started) {\n      if (g.taps < 2) touchfirst = p[0], touchstarting = setTimeout(function() {\n        touchstarting = null;\n      }, touchDelay);\n      interrupt(this);\n      g.start();\n    }\n  }\n  function touchmoved(event, ...args) {\n    if (!this.__zooming) return;\n    var g = gesture(this, args).event(event), touches = event.changedTouches, n = touches.length, i, t, p, l;\n    noevent(event);\n    for (i = 0; i < n; ++i) {\n      t = touches[i], p = pointer(t, this);\n      if (g.touch0 && g.touch0[2] === t.identifier) g.touch0[0] = p;\n      else if (g.touch1 && g.touch1[2] === t.identifier) g.touch1[0] = p;\n    }\n    t = g.that.__zoom;\n    if (g.touch1) {\n      var p0 = g.touch0[0], l0 = g.touch0[1], p1 = g.touch1[0], l1 = g.touch1[1], dp = (dp = p1[0] - p0[0]) * dp + (dp = p1[1] - p0[1]) * dp, dl = (dl = l1[0] - l0[0]) * dl + (dl = l1[1] - l0[1]) * dl;\n      t = scale(t, Math.sqrt(dp / dl));\n      p = [(p0[0] + p1[0]) / 2, (p0[1] + p1[1]) / 2];\n      l = [(l0[0] + l1[0]) / 2, (l0[1] + l1[1]) / 2];\n    } else if (g.touch0) p = g.touch0[0], l = g.touch0[1];\n    else return;\n    g.zoom(\"touch\", constrain(translate(t, p, l), g.extent, translateExtent));\n  }\n  function touchended(event, ...args) {\n    if (!this.__zooming) return;\n    var g = gesture(this, args).event(event), touches = event.changedTouches, n = touches.length, i, t;\n    nopropagation(event);\n    if (touchending) clearTimeout(touchending);\n    touchending = setTimeout(function() {\n      touchending = null;\n    }, touchDelay);\n    for (i = 0; i < n; ++i) {\n      t = touches[i];\n      if (g.touch0 && g.touch0[2] === t.identifier) delete g.touch0;\n      else if (g.touch1 && g.touch1[2] === t.identifier) delete g.touch1;\n    }\n    if (g.touch1 && !g.touch0) g.touch0 = g.touch1, delete g.touch1;\n    if (g.touch0) g.touch0[1] = this.__zoom.invert(g.touch0[0]);\n    else {\n      g.end();\n      if (g.taps === 2) {\n        t = pointer(t, this);\n        if (Math.hypot(touchfirst[0] - t[0], touchfirst[1] - t[1]) < tapDistance) {\n          var p = select(this).on(\"dblclick.zoom\");\n          if (p) p.apply(this, arguments);\n        }\n      }\n    }\n  }\n  zoom2.wheelDelta = function(_) {\n    return arguments.length ? (wheelDelta2 = typeof _ === \"function\" ? _ : constant(+_), zoom2) : wheelDelta2;\n  };\n  zoom2.filter = function(_) {\n    return arguments.length ? (filter2 = typeof _ === \"function\" ? _ : constant(!!_), zoom2) : filter2;\n  };\n  zoom2.touchable = function(_) {\n    return arguments.length ? (touchable = typeof _ === \"function\" ? _ : constant(!!_), zoom2) : touchable;\n  };\n  zoom2.extent = function(_) {\n    return arguments.length ? (extent = typeof _ === \"function\" ? _ : constant([[+_[0][0], +_[0][1]], [+_[1][0], +_[1][1]]]), zoom2) : extent;\n  };\n  zoom2.scaleExtent = function(_) {\n    return arguments.length ? (scaleExtent[0] = +_[0], scaleExtent[1] = +_[1], zoom2) : [scaleExtent[0], scaleExtent[1]];\n  };\n  zoom2.translateExtent = function(_) {\n    return arguments.length ? (translateExtent[0][0] = +_[0][0], translateExtent[1][0] = +_[1][0], translateExtent[0][1] = +_[0][1], translateExtent[1][1] = +_[1][1], zoom2) : [[translateExtent[0][0], translateExtent[0][1]], [translateExtent[1][0], translateExtent[1][1]]];\n  };\n  zoom2.constrain = function(_) {\n    return arguments.length ? (constrain = _, zoom2) : constrain;\n  };\n  zoom2.duration = function(_) {\n    return arguments.length ? (duration = +_, zoom2) : duration;\n  };\n  zoom2.interpolate = function(_) {\n    return arguments.length ? (interpolate2 = _, zoom2) : interpolate2;\n  };\n  zoom2.on = function() {\n    var value = listeners.on.apply(listeners, arguments);\n    return value === listeners ? zoom2 : value;\n  };\n  zoom2.clickDistance = function(_) {\n    return arguments.length ? (clickDistance2 = (_ = +_) * _, zoom2) : Math.sqrt(clickDistance2);\n  };\n  zoom2.tapDistance = function(_) {\n    return arguments.length ? (tapDistance = +_, zoom2) : tapDistance;\n  };\n  return zoom2;\n}\nconst Slots = Symbol(\"MiniMapSlots\");\nconst _hoisted_1$1 = [\"id\", \"x\", \"y\", \"rx\", \"ry\", \"width\", \"height\", \"fill\", \"stroke\", \"stroke-width\", \"shape-rendering\"];\nconst __default__$1 = {\n  name: \"MiniMapNode\",\n  compatConfig: { MODE: 3 },\n  inheritAttrs: false\n};\nconst _sfc_main$1 = /* @__PURE__ */ defineComponent({\n  ...__default__$1,\n  props: {\n    id: {},\n    type: {},\n    selected: { type: Boolean },\n    dragging: { type: Boolean },\n    position: {},\n    dimensions: {},\n    borderRadius: {},\n    color: {},\n    shapeRendering: {},\n    strokeColor: {},\n    strokeWidth: {},\n    hidden: { type: Boolean }\n  },\n  emits: [\"click\", \"dblclick\", \"mouseenter\", \"mousemove\", \"mouseleave\"],\n  setup(__props, { emit: emits }) {\n    const props = __props;\n    const miniMapSlots = inject(Slots);\n    const attrs = useAttrs();\n    const style = toRef(() => attrs.style ?? {});\n    function onClick(event) {\n      emits(\"click\", event);\n    }\n    function onDblclick(event) {\n      emits(\"dblclick\", event);\n    }\n    function onMouseEnter(event) {\n      emits(\"mouseenter\", event);\n    }\n    function onMouseMove(event) {\n      emits(\"mousemove\", event);\n    }\n    function onMouseLeave(event) {\n      emits(\"mouseleave\", event);\n    }\n    return (_ctx, _cache) => {\n      return !_ctx.hidden && _ctx.dimensions.width !== 0 && _ctx.dimensions.height !== 0 ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [\n        unref(miniMapSlots)[`node-${props.type}`] ? (openBlock(), createBlock(resolveDynamicComponent(unref(miniMapSlots)[`node-${props.type}`]), normalizeProps(mergeProps({ key: 0 }, { ...props, ..._ctx.$attrs })), null, 16)) : (openBlock(), createElementBlock(\"rect\", mergeProps({\n          key: 1,\n          id: _ctx.id\n        }, _ctx.$attrs, {\n          class: [\"vue-flow__minimap-node\", { selected: _ctx.selected, dragging: _ctx.dragging }],\n          x: _ctx.position.x,\n          y: _ctx.position.y,\n          rx: _ctx.borderRadius,\n          ry: _ctx.borderRadius,\n          width: _ctx.dimensions.width,\n          height: _ctx.dimensions.height,\n          fill: _ctx.color || style.value.background || style.value.backgroundColor,\n          stroke: _ctx.strokeColor,\n          \"stroke-width\": _ctx.strokeWidth,\n          \"shape-rendering\": _ctx.shapeRendering,\n          onClick,\n          onDblclick,\n          onMouseenter: onMouseEnter,\n          onMousemove: onMouseMove,\n          onMouseleave: onMouseLeave\n        }), null, 16, _hoisted_1$1))\n      ], 64)) : createCommentVNode(\"\", true);\n    };\n  }\n});\nconst _hoisted_1 = [\"width\", \"height\", \"viewBox\", \"aria-labelledby\"];\nconst _hoisted_2 = [\"id\"];\nconst _hoisted_3 = [\"d\", \"fill\", \"stroke\", \"stroke-width\"];\nconst __default__ = {\n  name: \"MiniMap\",\n  compatConfig: { MODE: 3 }\n};\nconst _sfc_main = /* @__PURE__ */ defineComponent({\n  ...__default__,\n  props: {\n    nodeColor: { type: [String, Function], default: \"#e2e2e2\" },\n    nodeStrokeColor: { type: [String, Function], default: \"transparent\" },\n    nodeClassName: { type: [String, Function] },\n    nodeBorderRadius: { default: 5 },\n    nodeStrokeWidth: { default: 2 },\n    maskColor: { default: \"rgb(240, 240, 240, 0.6)\" },\n    maskStrokeColor: { default: \"none\" },\n    maskStrokeWidth: { default: 1 },\n    position: { default: \"bottom-right\" },\n    pannable: { type: Boolean, default: false },\n    zoomable: { type: Boolean, default: false },\n    width: {},\n    height: {},\n    ariaLabel: { default: \"Vue Flow mini map\" },\n    inversePan: { type: Boolean, default: false },\n    zoomStep: { default: 1 },\n    offsetScale: { default: 5 },\n    maskBorderRadius: { default: 0 }\n  },\n  emits: [\"click\", \"nodeClick\", \"nodeDblclick\", \"nodeMouseenter\", \"nodeMousemove\", \"nodeMouseleave\"],\n  setup(__props, { emit }) {\n    const slots = useSlots();\n    const attrs = useAttrs();\n    const defaultWidth = 200;\n    const defaultHeight = 150;\n    const { id: id2, edges, viewport, translateExtent, dimensions, emits, d3Selection, d3Zoom, getNodesInitialized } = useVueFlow();\n    const el = ref();\n    provide(Slots, slots);\n    const elementWidth = toRef(() => {\n      var _a;\n      return __props.width ?? ((_a = attrs.style) == null ? void 0 : _a.width) ?? defaultWidth;\n    });\n    const elementHeight = toRef(() => {\n      var _a;\n      return __props.height ?? ((_a = attrs.style) == null ? void 0 : _a.height) ?? defaultHeight;\n    });\n    const shapeRendering = typeof window === \"undefined\" || !!window.chrome ? \"crispEdges\" : \"geometricPrecision\";\n    const nodeColorFunc = computed(() => typeof __props.nodeColor === \"string\" ? () => __props.nodeColor : __props.nodeColor);\n    const nodeStrokeColorFunc = computed(\n      () => typeof __props.nodeStrokeColor === \"string\" ? () => __props.nodeStrokeColor : __props.nodeStrokeColor\n    );\n    const nodeClassNameFunc = computed(\n      () => typeof __props.nodeClassName === \"string\" ? () => __props.nodeClassName : typeof __props.nodeClassName === \"function\" ? __props.nodeClassName : () => \"\"\n    );\n    const bb = computed(() => getRectOfNodes(getNodesInitialized.value.filter((node) => !node.hidden)));\n    const viewBB = computed(() => ({\n      x: -viewport.value.x / viewport.value.zoom,\n      y: -viewport.value.y / viewport.value.zoom,\n      width: dimensions.value.width / viewport.value.zoom,\n      height: dimensions.value.height / viewport.value.zoom\n    }));\n    const boundingRect = computed(\n      () => getNodesInitialized.value && getNodesInitialized.value.length ? getBoundsofRects(bb.value, viewBB.value) : viewBB.value\n    );\n    const viewScale = computed(() => {\n      const scaledWidth = boundingRect.value.width / elementWidth.value;\n      const scaledHeight = boundingRect.value.height / elementHeight.value;\n      return Math.max(scaledWidth, scaledHeight);\n    });\n    const viewBox = computed(() => {\n      const viewWidth = viewScale.value * elementWidth.value;\n      const viewHeight = viewScale.value * elementHeight.value;\n      const offset = __props.offsetScale * viewScale.value;\n      return {\n        offset,\n        x: boundingRect.value.x - (viewWidth - boundingRect.value.width) / 2 - offset,\n        y: boundingRect.value.y - (viewHeight - boundingRect.value.height) / 2 - offset,\n        width: viewWidth + offset * 2,\n        height: viewHeight + offset * 2\n      };\n    });\n    const d = computed(() => {\n      if (!viewBox.value.x || !viewBox.value.y) {\n        return \"\";\n      }\n      return `\n    M${viewBox.value.x - viewBox.value.offset},${viewBox.value.y - viewBox.value.offset}\n    h${viewBox.value.width + viewBox.value.offset * 2}\n    v${viewBox.value.height + viewBox.value.offset * 2}\n    h${-viewBox.value.width - viewBox.value.offset * 2}z\n    M${viewBB.value.x + __props.maskBorderRadius},${viewBB.value.y}\n    h${viewBB.value.width - 2 * __props.maskBorderRadius}\n    a${__props.maskBorderRadius},${__props.maskBorderRadius} 0 0 1 ${__props.maskBorderRadius},${__props.maskBorderRadius}\n    v${viewBB.value.height - 2 * __props.maskBorderRadius}\n    a${__props.maskBorderRadius},${__props.maskBorderRadius} 0 0 1 -${__props.maskBorderRadius},${__props.maskBorderRadius}\n    h${-(viewBB.value.width - 2 * __props.maskBorderRadius)}\n    a${__props.maskBorderRadius},${__props.maskBorderRadius} 0 0 1 -${__props.maskBorderRadius},-${__props.maskBorderRadius}\n    v${-(viewBB.value.height - 2 * __props.maskBorderRadius)}\n    a${__props.maskBorderRadius},${__props.maskBorderRadius} 0 0 1 ${__props.maskBorderRadius},-${__props.maskBorderRadius}z`;\n    });\n    watchEffect(\n      (onCleanup) => {\n        if (el.value) {\n          const selection2 = select(el.value);\n          const zoomHandler = (event) => {\n            if (event.sourceEvent.type !== \"wheel\" || !d3Selection.value || !d3Zoom.value) {\n              return;\n            }\n            const factor = event.sourceEvent.ctrlKey && isMacOs() ? 10 : 1;\n            const pinchDelta = -event.sourceEvent.deltaY * (event.sourceEvent.deltaMode === 1 ? 0.05 : event.sourceEvent.deltaMode ? 1 : 2e-3) * __props.zoomStep;\n            const nextZoom = viewport.value.zoom * 2 ** (pinchDelta * factor);\n            d3Zoom.value.scaleTo(d3Selection.value, nextZoom);\n          };\n          const panHandler = (event) => {\n            if (event.sourceEvent.type !== \"mousemove\" || !d3Selection.value || !d3Zoom.value) {\n              return;\n            }\n            const moveScale = viewScale.value * Math.max(1, viewport.value.zoom) * (__props.inversePan ? -1 : 1);\n            const position = {\n              x: viewport.value.x - event.sourceEvent.movementX * moveScale,\n              y: viewport.value.y - event.sourceEvent.movementY * moveScale\n            };\n            const extent = [\n              [0, 0],\n              [dimensions.value.width, dimensions.value.height]\n            ];\n            const nextTransform = identity.translate(position.x, position.y).scale(viewport.value.zoom);\n            const constrainedTransform = d3Zoom.value.constrain()(nextTransform, extent, translateExtent.value);\n            d3Zoom.value.transform(d3Selection.value, constrainedTransform);\n          };\n          const zoomAndPanHandler = zoom().wheelDelta((event) => wheelDelta(event) * (__props.zoomStep / 10)).on(\"zoom\", __props.pannable ? panHandler : () => {\n          }).on(\"zoom.wheel\", __props.zoomable ? zoomHandler : () => {\n          });\n          selection2.call(zoomAndPanHandler);\n          onCleanup(() => {\n            selection2.on(\"zoom\", null);\n          });\n        }\n      },\n      { flush: \"post\" }\n    );\n    function onSvgClick(event) {\n      const [x, y] = pointer(event);\n      emit(\"click\", { event, position: { x, y } });\n    }\n    function onNodeClick(event, node) {\n      const param = { event, node, connectedEdges: getConnectedEdges([node], edges.value) };\n      emits.miniMapNodeClick(param);\n      emit(\"nodeClick\", param);\n    }\n    function onNodeDblClick(event, node) {\n      const param = { event, node, connectedEdges: getConnectedEdges([node], edges.value) };\n      emits.miniMapNodeDoubleClick(param);\n      emit(\"nodeDblclick\", param);\n    }\n    function onNodeMouseEnter(event, node) {\n      const param = { event, node, connectedEdges: getConnectedEdges([node], edges.value) };\n      emits.miniMapNodeMouseEnter(param);\n      emit(\"nodeMouseenter\", param);\n    }\n    function onNodeMouseMove(event, node) {\n      const param = { event, node, connectedEdges: getConnectedEdges([node], edges.value) };\n      emits.miniMapNodeMouseMove(param);\n      emit(\"nodeMousemove\", param);\n    }\n    function onNodeMouseLeave(event, node) {\n      const param = { event, node, connectedEdges: getConnectedEdges([node], edges.value) };\n      emits.miniMapNodeMouseLeave(param);\n      emit(\"nodeMouseleave\", param);\n    }\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(unref(Panel), {\n        position: _ctx.position,\n        class: normalizeClass([\"vue-flow__minimap\", { pannable: _ctx.pannable, zoomable: _ctx.zoomable }])\n      }, {\n        default: withCtx(() => [\n          (openBlock(), createElementBlock(\"svg\", {\n            ref_key: \"el\",\n            ref: el,\n            width: elementWidth.value,\n            height: elementHeight.value,\n            viewBox: [viewBox.value.x, viewBox.value.y, viewBox.value.width, viewBox.value.height].join(\" \"),\n            role: \"img\",\n            \"aria-labelledby\": `vue-flow__minimap-${unref(id2)}`,\n            onClick: onSvgClick\n          }, [\n            _ctx.ariaLabel ? (openBlock(), createElementBlock(\"title\", {\n              key: 0,\n              id: `vue-flow__minimap-${unref(id2)}`\n            }, toDisplayString(_ctx.ariaLabel), 9, _hoisted_2)) : createCommentVNode(\"\", true),\n            (openBlock(true), createElementBlock(Fragment, null, renderList(unref(getNodesInitialized), (node) => {\n              return openBlock(), createBlock(_sfc_main$1, {\n                id: node.id,\n                key: node.id,\n                f: \"\",\n                position: node.computedPosition,\n                dimensions: node.dimensions,\n                selected: node.selected,\n                dragging: node.dragging,\n                style: normalizeStyle(node.style),\n                class: normalizeClass(nodeClassNameFunc.value(node)),\n                color: nodeColorFunc.value(node),\n                \"border-radius\": _ctx.nodeBorderRadius,\n                \"stroke-color\": nodeStrokeColorFunc.value(node),\n                \"stroke-width\": _ctx.nodeStrokeWidth,\n                \"shape-rendering\": unref(shapeRendering),\n                type: node.type,\n                hidden: node.hidden,\n                onClick: ($event) => onNodeClick($event, node),\n                onDblclick: ($event) => onNodeDblClick($event, node),\n                onMouseenter: ($event) => onNodeMouseEnter($event, node),\n                onMousemove: ($event) => onNodeMouseMove($event, node),\n                onMouseleave: ($event) => onNodeMouseLeave($event, node)\n              }, null, 8, [\"id\", \"position\", \"dimensions\", \"selected\", \"dragging\", \"style\", \"class\", \"color\", \"border-radius\", \"stroke-color\", \"stroke-width\", \"shape-rendering\", \"type\", \"hidden\", \"onClick\", \"onDblclick\", \"onMouseenter\", \"onMousemove\", \"onMouseleave\"]);\n            }), 128)),\n            createElementVNode(\"path\", {\n              class: \"vue-flow__minimap-mask\",\n              d: d.value,\n              fill: _ctx.maskColor,\n              stroke: _ctx.maskStrokeColor,\n              \"stroke-width\": _ctx.maskStrokeWidth,\n              \"fill-rule\": \"evenodd\"\n            }, null, 8, _hoisted_3)\n          ], 8, _hoisted_1))\n        ]),\n        _: 1\n      }, 8, [\"position\", \"class\"]);\n    };\n  }\n});\nexport {\n  _sfc_main as MiniMap,\n  _sfc_main$1 as MiniMapNode,\n  Slots\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAI,OAAO,EAAE,OAAO,MAAM;AAC1B,EAAE;AACF,SAAS,WAAW;AAClB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG,EAAE,GAAG;AAC3D,QAAI,EAAE,IAAI,UAAU,CAAC,IAAI,OAAO,KAAK,KAAK,QAAQ,KAAK,CAAC,EAAG,OAAM,IAAI,MAAM,mBAAmB,CAAC;AAC/F,MAAE,CAAC,IAAI,CAAC;AAAA,EACV;AACA,SAAO,IAAI,SAAS,CAAC;AACvB;AACA,SAAS,SAAS,GAAG;AACnB,OAAK,IAAI;AACX;AACA,SAAS,iBAAiB,WAAW,OAAO;AAC1C,SAAO,UAAU,KAAK,EAAE,MAAM,OAAO,EAAE,IAAI,SAAS,GAAG;AACrD,QAAI,OAAO,IAAI,IAAI,EAAE,QAAQ,GAAG;AAChC,QAAI,KAAK,EAAG,QAAO,EAAE,MAAM,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,GAAG,CAAC;AACnD,QAAI,KAAK,CAAC,MAAM,eAAe,CAAC,EAAG,OAAM,IAAI,MAAM,mBAAmB,CAAC;AACvE,WAAO,EAAE,MAAM,GAAG,KAAK;AAAA,EACzB,CAAC;AACH;AACA,SAAS,YAAY,SAAS,YAAY;AAAA,EACxC,aAAa;AAAA,EACb,IAAI,SAAS,UAAU,UAAU;AAC/B,QAAI,IAAI,KAAK,GAAG,IAAI,iBAAiB,WAAW,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,EAAE;AACzE,QAAI,UAAU,SAAS,GAAG;AACxB,aAAO,EAAE,IAAI,EAAG,MAAK,KAAK,WAAW,EAAE,CAAC,GAAG,UAAU,IAAI,MAAM,EAAE,CAAC,GAAG,SAAS,IAAI,GAAI,QAAO;AAC7F;AAAA,IACF;AACA,QAAI,YAAY,QAAQ,OAAO,aAAa,WAAY,OAAM,IAAI,MAAM,uBAAuB,QAAQ;AACvG,WAAO,EAAE,IAAI,GAAG;AACd,UAAI,KAAK,WAAW,EAAE,CAAC,GAAG,KAAM,GAAE,CAAC,IAAI,MAAM,EAAE,CAAC,GAAG,SAAS,MAAM,QAAQ;AAAA,eACjE,YAAY,KAAM,MAAK,KAAK,EAAG,GAAE,CAAC,IAAI,MAAM,EAAE,CAAC,GAAG,SAAS,MAAM,IAAI;AAAA,IAChF;AACA,WAAO;AAAA,EACT;AAAA,EACA,MAAM,WAAW;AACf,QAAI,OAAO,CAAC,GAAG,IAAI,KAAK;AACxB,aAAS,KAAK,EAAG,MAAK,CAAC,IAAI,EAAE,CAAC,EAAE,MAAM;AACtC,WAAO,IAAI,SAAS,IAAI;AAAA,EAC1B;AAAA,EACA,MAAM,SAAS,MAAM,MAAM;AACzB,SAAK,IAAI,UAAU,SAAS,KAAK,EAAG,UAAS,OAAO,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,EAAE,EAAG,MAAK,CAAC,IAAI,UAAU,IAAI,CAAC;AACpH,QAAI,CAAC,KAAK,EAAE,eAAe,IAAI,EAAG,OAAM,IAAI,MAAM,mBAAmB,IAAI;AACzE,SAAK,IAAI,KAAK,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAI,GAAG,EAAE,EAAG,GAAE,CAAC,EAAE,MAAM,MAAM,MAAM,IAAI;AAAA,EACrF;AAAA,EACA,OAAO,SAAS,MAAM,MAAM,MAAM;AAChC,QAAI,CAAC,KAAK,EAAE,eAAe,IAAI,EAAG,OAAM,IAAI,MAAM,mBAAmB,IAAI;AACzE,aAAS,IAAI,KAAK,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAI,GAAG,EAAE,EAAG,GAAE,CAAC,EAAE,MAAM,MAAM,MAAM,IAAI;AAAA,EACzF;AACF;AACA,SAAS,MAAM,MAAM,MAAM;AACzB,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,GAAG,IAAI,GAAG,EAAE,GAAG;AAC9C,SAAK,IAAI,KAAK,CAAC,GAAG,SAAS,MAAM;AAC/B,aAAO,EAAE;AAAA,IACX;AAAA,EACF;AACF;AACA,SAAS,MAAM,MAAM,MAAM,UAAU;AACnC,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC3C,QAAI,KAAK,CAAC,EAAE,SAAS,MAAM;AACzB,WAAK,CAAC,IAAI,MAAM,OAAO,KAAK,MAAM,GAAG,CAAC,EAAE,OAAO,KAAK,MAAM,IAAI,CAAC,CAAC;AAChE;AAAA,IACF;AAAA,EACF;AACA,MAAI,YAAY,KAAM,MAAK,KAAK,EAAE,MAAM,OAAO,SAAS,CAAC;AACzD,SAAO;AACT;AACA,IAAI,QAAQ;AACZ,IAAM,aAAa;AAAA,EACjB,KAAK;AAAA,EACL;AAAA,EACA,OAAO;AAAA,EACP,KAAK;AAAA,EACL,OAAO;AACT;AACA,SAAS,UAAU,MAAM;AACvB,MAAI,SAAS,QAAQ,IAAI,IAAI,OAAO,QAAQ,GAAG;AAC/C,MAAI,KAAK,MAAM,SAAS,KAAK,MAAM,GAAG,CAAC,OAAO,QAAS,QAAO,KAAK,MAAM,IAAI,CAAC;AAC9E,SAAO,WAAW,eAAe,MAAM,IAAI,EAAE,OAAO,WAAW,MAAM,GAAG,OAAO,KAAK,IAAI;AAC1F;AACA,SAAS,eAAe,MAAM;AAC5B,SAAO,WAAW;AAChB,QAAI,YAAY,KAAK,eAAe,MAAM,KAAK;AAC/C,WAAO,QAAQ,SAAS,UAAU,gBAAgB,iBAAiB,QAAQ,UAAU,cAAc,IAAI,IAAI,UAAU,gBAAgB,KAAK,IAAI;AAAA,EAChJ;AACF;AACA,SAAS,aAAa,UAAU;AAC9B,SAAO,WAAW;AAChB,WAAO,KAAK,cAAc,gBAAgB,SAAS,OAAO,SAAS,KAAK;AAAA,EAC1E;AACF;AACA,SAAS,QAAQ,MAAM;AACrB,MAAI,WAAW,UAAU,IAAI;AAC7B,UAAQ,SAAS,QAAQ,eAAe,gBAAgB,QAAQ;AAClE;AACA,SAAS,OAAO;AAChB;AACA,SAAS,SAAS,WAAW;AAC3B,SAAO,aAAa,OAAO,OAAO,WAAW;AAC3C,WAAO,KAAK,cAAc,SAAS;AAAA,EACrC;AACF;AACA,SAAS,iBAAiB,SAAS;AACjC,MAAI,OAAO,YAAY,WAAY,WAAU,SAAS,OAAO;AAC7D,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,YAAY,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC9F,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,WAAW,UAAU,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,MAAM,SAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtH,WAAK,OAAO,MAAM,CAAC,OAAO,UAAU,QAAQ,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,IAAI;AAChF,YAAI,cAAc,KAAM,SAAQ,WAAW,KAAK;AAChD,iBAAS,CAAC,IAAI;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI,YAAY,WAAW,KAAK,QAAQ;AACjD;AACA,SAAS,MAAM,GAAG;AAChB,SAAO,KAAK,OAAO,CAAC,IAAI,MAAM,QAAQ,CAAC,IAAI,IAAI,MAAM,KAAK,CAAC;AAC7D;AACA,SAAS,QAAQ;AACf,SAAO,CAAC;AACV;AACA,SAAS,YAAY,WAAW;AAC9B,SAAO,aAAa,OAAO,QAAQ,WAAW;AAC5C,WAAO,KAAK,iBAAiB,SAAS;AAAA,EACxC;AACF;AACA,SAAS,SAAS,SAAS;AACzB,SAAO,WAAW;AAChB,WAAO,MAAM,QAAQ,MAAM,MAAM,SAAS,CAAC;AAAA,EAC7C;AACF;AACA,SAAS,oBAAoB,SAAS;AACpC,MAAI,OAAO,YAAY,WAAY,WAAU,SAAS,OAAO;AAAA,MACxD,WAAU,YAAY,OAAO;AAClC,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,YAAY,CAAC,GAAG,UAAU,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAClG,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAO,MAAM,CAAC,GAAG;AACnB,kBAAU,KAAK,QAAQ,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,CAAC;AAC1D,gBAAQ,KAAK,IAAI;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI,YAAY,WAAW,OAAO;AAC3C;AACA,SAAS,QAAQ,WAAW;AAC1B,SAAO,WAAW;AAChB,WAAO,KAAK,QAAQ,SAAS;AAAA,EAC/B;AACF;AACA,SAAS,aAAa,WAAW;AAC/B,SAAO,SAAS,MAAM;AACpB,WAAO,KAAK,QAAQ,SAAS;AAAA,EAC/B;AACF;AACA,IAAI,OAAO,MAAM,UAAU;AAC3B,SAAS,UAAU,OAAO;AACxB,SAAO,WAAW;AAChB,WAAO,KAAK,KAAK,KAAK,UAAU,KAAK;AAAA,EACvC;AACF;AACA,SAAS,aAAa;AACpB,SAAO,KAAK;AACd;AACA,SAAS,sBAAsB,OAAO;AACpC,SAAO,KAAK,OAAO,SAAS,OAAO,aAAa,UAAU,OAAO,UAAU,aAAa,QAAQ,aAAa,KAAK,CAAC,CAAC;AACtH;AACA,IAAI,SAAS,MAAM,UAAU;AAC7B,SAAS,WAAW;AAClB,SAAO,MAAM,KAAK,KAAK,QAAQ;AACjC;AACA,SAAS,eAAe,OAAO;AAC7B,SAAO,WAAW;AAChB,WAAO,OAAO,KAAK,KAAK,UAAU,KAAK;AAAA,EACzC;AACF;AACA,SAAS,yBAAyB,OAAO;AACvC,SAAO,KAAK,UAAU,SAAS,OAAO,WAAW,eAAe,OAAO,UAAU,aAAa,QAAQ,aAAa,KAAK,CAAC,CAAC;AAC5H;AACA,SAAS,iBAAiB,OAAO;AAC/B,MAAI,OAAO,UAAU,WAAY,SAAQ,QAAQ,KAAK;AACtD,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,YAAY,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC9F,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,WAAW,UAAU,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACnG,WAAK,OAAO,MAAM,CAAC,MAAM,MAAM,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,GAAG;AAClE,iBAAS,KAAK,IAAI;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI,YAAY,WAAW,KAAK,QAAQ;AACjD;AACA,SAAS,OAAO,QAAQ;AACtB,SAAO,IAAI,MAAM,OAAO,MAAM;AAChC;AACA,SAAS,kBAAkB;AACzB,SAAO,IAAI,YAAY,KAAK,UAAU,KAAK,QAAQ,IAAI,MAAM,GAAG,KAAK,QAAQ;AAC/E;AACA,SAAS,UAAU,QAAQ,QAAQ;AACjC,OAAK,gBAAgB,OAAO;AAC5B,OAAK,eAAe,OAAO;AAC3B,OAAK,QAAQ;AACb,OAAK,UAAU;AACf,OAAK,WAAW;AAClB;AACA,UAAU,YAAY;AAAA,EACpB,aAAa;AAAA,EACb,aAAa,SAAS,OAAO;AAC3B,WAAO,KAAK,QAAQ,aAAa,OAAO,KAAK,KAAK;AAAA,EACpD;AAAA,EACA,cAAc,SAAS,OAAO,MAAM;AAClC,WAAO,KAAK,QAAQ,aAAa,OAAO,IAAI;AAAA,EAC9C;AAAA,EACA,eAAe,SAAS,WAAW;AACjC,WAAO,KAAK,QAAQ,cAAc,SAAS;AAAA,EAC7C;AAAA,EACA,kBAAkB,SAAS,WAAW;AACpC,WAAO,KAAK,QAAQ,iBAAiB,SAAS;AAAA,EAChD;AACF;AACA,SAAS,WAAW,GAAG;AACrB,SAAO,WAAW;AAChB,WAAO;AAAA,EACT;AACF;AACA,SAAS,UAAU,QAAQ,OAAO,OAAO,QAAQ,MAAM,MAAM;AAC3D,MAAI,IAAI,GAAG,MAAM,cAAc,MAAM,QAAQ,aAAa,KAAK;AAC/D,SAAO,IAAI,YAAY,EAAE,GAAG;AAC1B,QAAI,OAAO,MAAM,CAAC,GAAG;AACnB,WAAK,WAAW,KAAK,CAAC;AACtB,aAAO,CAAC,IAAI;AAAA,IACd,OAAO;AACL,YAAM,CAAC,IAAI,IAAI,UAAU,QAAQ,KAAK,CAAC,CAAC;AAAA,IAC1C;AAAA,EACF;AACA,SAAO,IAAI,aAAa,EAAE,GAAG;AAC3B,QAAI,OAAO,MAAM,CAAC,GAAG;AACnB,WAAK,CAAC,IAAI;AAAA,IACZ;AAAA,EACF;AACF;AACA,SAAS,QAAQ,QAAQ,OAAO,OAAO,QAAQ,MAAM,MAAM,KAAK;AAC9D,MAAI,GAAG,MAAM,iBAAiC,oBAAI,IAAI,GAAG,cAAc,MAAM,QAAQ,aAAa,KAAK,QAAQ,YAAY,IAAI,MAAM,WAAW,GAAG;AACnJ,OAAK,IAAI,GAAG,IAAI,aAAa,EAAE,GAAG;AAChC,QAAI,OAAO,MAAM,CAAC,GAAG;AACnB,gBAAU,CAAC,IAAI,WAAW,IAAI,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,IAAI;AACpE,UAAI,eAAe,IAAI,QAAQ,GAAG;AAChC,aAAK,CAAC,IAAI;AAAA,MACZ,OAAO;AACL,uBAAe,IAAI,UAAU,IAAI;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AACA,OAAK,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AAC/B,eAAW,IAAI,KAAK,QAAQ,KAAK,CAAC,GAAG,GAAG,IAAI,IAAI;AAChD,QAAI,OAAO,eAAe,IAAI,QAAQ,GAAG;AACvC,aAAO,CAAC,IAAI;AACZ,WAAK,WAAW,KAAK,CAAC;AACtB,qBAAe,OAAO,QAAQ;AAAA,IAChC,OAAO;AACL,YAAM,CAAC,IAAI,IAAI,UAAU,QAAQ,KAAK,CAAC,CAAC;AAAA,IAC1C;AAAA,EACF;AACA,OAAK,IAAI,GAAG,IAAI,aAAa,EAAE,GAAG;AAChC,SAAK,OAAO,MAAM,CAAC,MAAM,eAAe,IAAI,UAAU,CAAC,CAAC,MAAM,MAAM;AAClE,WAAK,CAAC,IAAI;AAAA,IACZ;AAAA,EACF;AACF;AACA,SAAS,MAAM,MAAM;AACnB,SAAO,KAAK;AACd;AACA,SAAS,eAAe,OAAO,KAAK;AAClC,MAAI,CAAC,UAAU,OAAQ,QAAO,MAAM,KAAK,MAAM,KAAK;AACpD,MAAI,OAAO,MAAM,UAAU,WAAW,UAAU,KAAK,UAAU,SAAS,KAAK;AAC7E,MAAI,OAAO,UAAU,WAAY,SAAQ,WAAW,KAAK;AACzD,WAAS,IAAI,OAAO,QAAQ,SAAS,IAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,MAAM,CAAC,GAAG,OAAO,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/G,QAAI,SAAS,QAAQ,CAAC,GAAG,QAAQ,OAAO,CAAC,GAAG,cAAc,MAAM,QAAQ,OAAO,UAAU,MAAM,KAAK,QAAQ,UAAU,OAAO,UAAU,GAAG,OAAO,CAAC,GAAG,aAAa,KAAK,QAAQ,aAAa,MAAM,CAAC,IAAI,IAAI,MAAM,UAAU,GAAG,cAAc,OAAO,CAAC,IAAI,IAAI,MAAM,UAAU,GAAG,YAAY,KAAK,CAAC,IAAI,IAAI,MAAM,WAAW;AAC1T,SAAK,QAAQ,OAAO,YAAY,aAAa,WAAW,MAAM,GAAG;AACjE,aAAS,KAAK,GAAG,KAAK,GAAG,UAAU,MAAM,KAAK,YAAY,EAAE,IAAI;AAC9D,UAAI,WAAW,WAAW,EAAE,GAAG;AAC7B,YAAI,MAAM,GAAI,MAAK,KAAK;AACxB,eAAO,EAAE,OAAO,YAAY,EAAE,MAAM,EAAE,KAAK,WAAY;AACvD,iBAAS,QAAQ,QAAQ;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AACA,WAAS,IAAI,YAAY,QAAQ,OAAO;AACxC,SAAO,SAAS;AAChB,SAAO,QAAQ;AACf,SAAO;AACT;AACA,SAAS,UAAU,MAAM;AACvB,SAAO,OAAO,SAAS,YAAY,YAAY,OAAO,OAAO,MAAM,KAAK,IAAI;AAC9E;AACA,SAAS,iBAAiB;AACxB,SAAO,IAAI,YAAY,KAAK,SAAS,KAAK,QAAQ,IAAI,MAAM,GAAG,KAAK,QAAQ;AAC9E;AACA,SAAS,eAAe,SAAS,UAAU,QAAQ;AACjD,MAAI,QAAQ,KAAK,MAAM,GAAG,SAAS,MAAM,OAAO,KAAK,KAAK;AAC1D,MAAI,OAAO,YAAY,YAAY;AACjC,YAAQ,QAAQ,KAAK;AACrB,QAAI,MAAO,SAAQ,MAAM,UAAU;AAAA,EACrC,OAAO;AACL,YAAQ,MAAM,OAAO,UAAU,EAAE;AAAA,EACnC;AACA,MAAI,YAAY,MAAM;AACpB,aAAS,SAAS,MAAM;AACxB,QAAI,OAAQ,UAAS,OAAO,UAAU;AAAA,EACxC;AACA,MAAI,UAAU,KAAM,MAAK,OAAO;AAAA,MAC3B,QAAO,IAAI;AAChB,SAAO,SAAS,SAAS,MAAM,MAAM,MAAM,EAAE,MAAM,IAAI;AACzD;AACA,SAAS,gBAAgB,SAAS;AAChC,MAAI,aAAa,QAAQ,YAAY,QAAQ,UAAU,IAAI;AAC3D,WAAS,UAAU,KAAK,SAAS,UAAU,WAAW,SAAS,KAAK,QAAQ,QAAQ,KAAK,QAAQ,QAAQ,IAAI,KAAK,IAAI,IAAI,EAAE,GAAG,SAAS,IAAI,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACxK,aAAS,SAAS,QAAQ,CAAC,GAAG,SAAS,QAAQ,CAAC,GAAG,IAAI,OAAO,QAAQ,QAAQ,OAAO,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/H,UAAI,OAAO,OAAO,CAAC,KAAK,OAAO,CAAC,GAAG;AACjC,cAAM,CAAC,IAAI;AAAA,MACb;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI,IAAI,EAAE,GAAG;AAClB,WAAO,CAAC,IAAI,QAAQ,CAAC;AAAA,EACvB;AACA,SAAO,IAAI,YAAY,QAAQ,KAAK,QAAQ;AAC9C;AACA,SAAS,kBAAkB;AACzB,WAAS,SAAS,KAAK,SAAS,IAAI,IAAI,IAAI,OAAO,QAAQ,EAAE,IAAI,KAAK;AACpE,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,SAAS,GAAG,OAAO,MAAM,CAAC,GAAG,MAAM,EAAE,KAAK,KAAK;AACnF,UAAI,OAAO,MAAM,CAAC,GAAG;AACnB,YAAI,QAAQ,KAAK,wBAAwB,IAAI,IAAI,EAAG,MAAK,WAAW,aAAa,MAAM,IAAI;AAC3F,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,eAAe,SAAS;AAC/B,MAAI,CAAC,QAAS,WAAU;AACxB,WAAS,YAAY,GAAG,GAAG;AACzB,WAAO,KAAK,IAAI,QAAQ,EAAE,UAAU,EAAE,QAAQ,IAAI,CAAC,IAAI,CAAC;AAAA,EAC1D;AACA,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,aAAa,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/F,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,YAAY,WAAW,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/G,UAAI,OAAO,MAAM,CAAC,GAAG;AACnB,kBAAU,CAAC,IAAI;AAAA,MACjB;AAAA,IACF;AACA,cAAU,KAAK,WAAW;AAAA,EAC5B;AACA,SAAO,IAAI,YAAY,YAAY,KAAK,QAAQ,EAAE,MAAM;AAC1D;AACA,SAAS,UAAU,GAAG,GAAG;AACvB,SAAO,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;AAC/C;AACA,SAAS,iBAAiB;AACxB,MAAI,WAAW,UAAU,CAAC;AAC1B,YAAU,CAAC,IAAI;AACf,WAAS,MAAM,MAAM,SAAS;AAC9B,SAAO;AACT;AACA,SAAS,kBAAkB;AACzB,SAAO,MAAM,KAAK,IAAI;AACxB;AACA,SAAS,iBAAiB;AACxB,WAAS,SAAS,KAAK,SAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AACpE,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC/D,UAAI,OAAO,MAAM,CAAC;AAClB,UAAI,KAAM,QAAO;AAAA,IACnB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,iBAAiB;AACxB,MAAI,OAAO;AACX,aAAW,QAAQ,KAAM,GAAE;AAC3B,SAAO;AACT;AACA,SAAS,kBAAkB;AACzB,SAAO,CAAC,KAAK,KAAK;AACpB;AACA,SAAS,eAAe,UAAU;AAChC,WAAS,SAAS,KAAK,SAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AACpE,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAO,MAAM,CAAC,EAAG,UAAS,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK;AAAA,IAClE;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,aAAa,MAAM;AAC1B,SAAO,WAAW;AAChB,SAAK,gBAAgB,IAAI;AAAA,EAC3B;AACF;AACA,SAAS,eAAe,UAAU;AAChC,SAAO,WAAW;AAChB,SAAK,kBAAkB,SAAS,OAAO,SAAS,KAAK;AAAA,EACvD;AACF;AACA,SAAS,eAAe,MAAM,OAAO;AACnC,SAAO,WAAW;AAChB,SAAK,aAAa,MAAM,KAAK;AAAA,EAC/B;AACF;AACA,SAAS,iBAAiB,UAAU,OAAO;AACzC,SAAO,WAAW;AAChB,SAAK,eAAe,SAAS,OAAO,SAAS,OAAO,KAAK;AAAA,EAC3D;AACF;AACA,SAAS,eAAe,MAAM,OAAO;AACnC,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,KAAK,KAAM,MAAK,gBAAgB,IAAI;AAAA,QACnC,MAAK,aAAa,MAAM,CAAC;AAAA,EAChC;AACF;AACA,SAAS,iBAAiB,UAAU,OAAO;AACzC,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,KAAK,KAAM,MAAK,kBAAkB,SAAS,OAAO,SAAS,KAAK;AAAA,QAC/D,MAAK,eAAe,SAAS,OAAO,SAAS,OAAO,CAAC;AAAA,EAC5D;AACF;AACA,SAAS,eAAe,MAAM,OAAO;AACnC,MAAI,WAAW,UAAU,IAAI;AAC7B,MAAI,UAAU,SAAS,GAAG;AACxB,QAAI,OAAO,KAAK,KAAK;AACrB,WAAO,SAAS,QAAQ,KAAK,eAAe,SAAS,OAAO,SAAS,KAAK,IAAI,KAAK,aAAa,QAAQ;AAAA,EAC1G;AACA,SAAO,KAAK,MAAM,SAAS,OAAO,SAAS,QAAQ,iBAAiB,eAAe,OAAO,UAAU,aAAa,SAAS,QAAQ,mBAAmB,iBAAiB,SAAS,QAAQ,mBAAmB,gBAAgB,UAAU,KAAK,CAAC;AAC5O;AACA,SAAS,YAAY,MAAM;AACzB,SAAO,KAAK,iBAAiB,KAAK,cAAc,eAAe,KAAK,YAAY,QAAQ,KAAK;AAC/F;AACA,SAAS,cAAc,MAAM;AAC3B,SAAO,WAAW;AAChB,SAAK,MAAM,eAAe,IAAI;AAAA,EAChC;AACF;AACA,SAAS,gBAAgB,MAAM,OAAO,UAAU;AAC9C,SAAO,WAAW;AAChB,SAAK,MAAM,YAAY,MAAM,OAAO,QAAQ;AAAA,EAC9C;AACF;AACA,SAAS,gBAAgB,MAAM,OAAO,UAAU;AAC9C,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,KAAK,KAAM,MAAK,MAAM,eAAe,IAAI;AAAA,QACxC,MAAK,MAAM,YAAY,MAAM,GAAG,QAAQ;AAAA,EAC/C;AACF;AACA,SAAS,gBAAgB,MAAM,OAAO,UAAU;AAC9C,SAAO,UAAU,SAAS,IAAI,KAAK,MAAM,SAAS,OAAO,gBAAgB,OAAO,UAAU,aAAa,kBAAkB,iBAAiB,MAAM,OAAO,YAAY,OAAO,KAAK,QAAQ,CAAC,IAAI,WAAW,KAAK,KAAK,GAAG,IAAI;AAC1N;AACA,SAAS,WAAW,MAAM,MAAM;AAC9B,SAAO,KAAK,MAAM,iBAAiB,IAAI,KAAK,YAAY,IAAI,EAAE,iBAAiB,MAAM,IAAI,EAAE,iBAAiB,IAAI;AAClH;AACA,SAAS,eAAe,MAAM;AAC5B,SAAO,WAAW;AAChB,WAAO,KAAK,IAAI;AAAA,EAClB;AACF;AACA,SAAS,iBAAiB,MAAM,OAAO;AACrC,SAAO,WAAW;AAChB,SAAK,IAAI,IAAI;AAAA,EACf;AACF;AACA,SAAS,iBAAiB,MAAM,OAAO;AACrC,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,KAAK,KAAM,QAAO,KAAK,IAAI;AAAA,QAC1B,MAAK,IAAI,IAAI;AAAA,EACpB;AACF;AACA,SAAS,mBAAmB,MAAM,OAAO;AACvC,SAAO,UAAU,SAAS,IAAI,KAAK,MAAM,SAAS,OAAO,iBAAiB,OAAO,UAAU,aAAa,mBAAmB,kBAAkB,MAAM,KAAK,CAAC,IAAI,KAAK,KAAK,EAAE,IAAI;AAC/K;AACA,SAAS,WAAW,QAAQ;AAC1B,SAAO,OAAO,KAAK,EAAE,MAAM,OAAO;AACpC;AACA,SAAS,UAAU,MAAM;AACvB,SAAO,KAAK,aAAa,IAAI,UAAU,IAAI;AAC7C;AACA,SAAS,UAAU,MAAM;AACvB,OAAK,QAAQ;AACb,OAAK,SAAS,WAAW,KAAK,aAAa,OAAO,KAAK,EAAE;AAC3D;AACA,UAAU,YAAY;AAAA,EACpB,KAAK,SAAS,MAAM;AAClB,QAAI,IAAI,KAAK,OAAO,QAAQ,IAAI;AAChC,QAAI,IAAI,GAAG;AACT,WAAK,OAAO,KAAK,IAAI;AACrB,WAAK,MAAM,aAAa,SAAS,KAAK,OAAO,KAAK,GAAG,CAAC;AAAA,IACxD;AAAA,EACF;AAAA,EACA,QAAQ,SAAS,MAAM;AACrB,QAAI,IAAI,KAAK,OAAO,QAAQ,IAAI;AAChC,QAAI,KAAK,GAAG;AACV,WAAK,OAAO,OAAO,GAAG,CAAC;AACvB,WAAK,MAAM,aAAa,SAAS,KAAK,OAAO,KAAK,GAAG,CAAC;AAAA,IACxD;AAAA,EACF;AAAA,EACA,UAAU,SAAS,MAAM;AACvB,WAAO,KAAK,OAAO,QAAQ,IAAI,KAAK;AAAA,EACtC;AACF;AACA,SAAS,WAAW,MAAM,OAAO;AAC/B,MAAI,OAAO,UAAU,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM;AAC9C,SAAO,EAAE,IAAI,EAAG,MAAK,IAAI,MAAM,CAAC,CAAC;AACnC;AACA,SAAS,cAAc,MAAM,OAAO;AAClC,MAAI,OAAO,UAAU,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM;AAC9C,SAAO,EAAE,IAAI,EAAG,MAAK,OAAO,MAAM,CAAC,CAAC;AACtC;AACA,SAAS,YAAY,OAAO;AAC1B,SAAO,WAAW;AAChB,eAAW,MAAM,KAAK;AAAA,EACxB;AACF;AACA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,kBAAc,MAAM,KAAK;AAAA,EAC3B;AACF;AACA,SAAS,gBAAgB,OAAO,OAAO;AACrC,SAAO,WAAW;AAChB,KAAC,MAAM,MAAM,MAAM,SAAS,IAAI,aAAa,eAAe,MAAM,KAAK;AAAA,EACzE;AACF;AACA,SAAS,kBAAkB,MAAM,OAAO;AACtC,MAAI,QAAQ,WAAW,OAAO,EAAE;AAChC,MAAI,UAAU,SAAS,GAAG;AACxB,QAAI,OAAO,UAAU,KAAK,KAAK,CAAC,GAAG,IAAI,IAAI,IAAI,MAAM;AACrD,WAAO,EAAE,IAAI,EAAG,KAAI,CAAC,KAAK,SAAS,MAAM,CAAC,CAAC,EAAG,QAAO;AACrD,WAAO;AAAA,EACT;AACA,SAAO,KAAK,MAAM,OAAO,UAAU,aAAa,kBAAkB,QAAQ,cAAc,cAAc,OAAO,KAAK,CAAC;AACrH;AACA,SAAS,aAAa;AACpB,OAAK,cAAc;AACrB;AACA,SAAS,eAAe,OAAO;AAC7B,SAAO,WAAW;AAChB,SAAK,cAAc;AAAA,EACrB;AACF;AACA,SAAS,eAAe,OAAO;AAC7B,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,SAAK,cAAc,KAAK,OAAO,KAAK;AAAA,EACtC;AACF;AACA,SAAS,eAAe,OAAO;AAC7B,SAAO,UAAU,SAAS,KAAK,KAAK,SAAS,OAAO,cAAc,OAAO,UAAU,aAAa,iBAAiB,gBAAgB,KAAK,CAAC,IAAI,KAAK,KAAK,EAAE;AACzJ;AACA,SAAS,aAAa;AACpB,OAAK,YAAY;AACnB;AACA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,SAAK,YAAY;AAAA,EACnB;AACF;AACA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,SAAK,YAAY,KAAK,OAAO,KAAK;AAAA,EACpC;AACF;AACA,SAAS,eAAe,OAAO;AAC7B,SAAO,UAAU,SAAS,KAAK,KAAK,SAAS,OAAO,cAAc,OAAO,UAAU,aAAa,eAAe,cAAc,KAAK,CAAC,IAAI,KAAK,KAAK,EAAE;AACrJ;AACA,SAAS,QAAQ;AACf,MAAI,KAAK,YAAa,MAAK,WAAW,YAAY,IAAI;AACxD;AACA,SAAS,kBAAkB;AACzB,SAAO,KAAK,KAAK,KAAK;AACxB;AACA,SAAS,QAAQ;AACf,MAAI,KAAK,gBAAiB,MAAK,WAAW,aAAa,MAAM,KAAK,WAAW,UAAU;AACzF;AACA,SAAS,kBAAkB;AACzB,SAAO,KAAK,KAAK,KAAK;AACxB;AACA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,UAAU,OAAO,SAAS,aAAa,OAAO,QAAQ,IAAI;AAC9D,SAAO,KAAK,OAAO,WAAW;AAC5B,WAAO,KAAK,YAAY,QAAQ,MAAM,MAAM,SAAS,CAAC;AAAA,EACxD,CAAC;AACH;AACA,SAAS,eAAe;AACtB,SAAO;AACT;AACA,SAAS,iBAAiB,MAAM,QAAQ;AACtC,MAAI,UAAU,OAAO,SAAS,aAAa,OAAO,QAAQ,IAAI,GAAG,UAAU,UAAU,OAAO,eAAe,OAAO,WAAW,aAAa,SAAS,SAAS,MAAM;AAClK,SAAO,KAAK,OAAO,WAAW;AAC5B,WAAO,KAAK,aAAa,QAAQ,MAAM,MAAM,SAAS,GAAG,QAAQ,MAAM,MAAM,SAAS,KAAK,IAAI;AAAA,EACjG,CAAC;AACH;AACA,SAAS,SAAS;AAChB,MAAI,SAAS,KAAK;AAClB,MAAI,OAAQ,QAAO,YAAY,IAAI;AACrC;AACA,SAAS,mBAAmB;AAC1B,SAAO,KAAK,KAAK,MAAM;AACzB;AACA,SAAS,yBAAyB;AAChC,MAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,SAAS,KAAK;AACjD,SAAO,SAAS,OAAO,aAAa,OAAO,KAAK,WAAW,IAAI;AACjE;AACA,SAAS,sBAAsB;AAC7B,MAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,SAAS,KAAK;AAChD,SAAO,SAAS,OAAO,aAAa,OAAO,KAAK,WAAW,IAAI;AACjE;AACA,SAAS,gBAAgB,MAAM;AAC7B,SAAO,KAAK,OAAO,OAAO,sBAAsB,sBAAsB;AACxE;AACA,SAAS,gBAAgB,OAAO;AAC9B,SAAO,UAAU,SAAS,KAAK,SAAS,YAAY,KAAK,IAAI,KAAK,KAAK,EAAE;AAC3E;AACA,SAAS,gBAAgB,UAAU;AACjC,SAAO,SAAS,OAAO;AACrB,aAAS,KAAK,MAAM,OAAO,KAAK,QAAQ;AAAA,EAC1C;AACF;AACA,SAAS,eAAe,WAAW;AACjC,SAAO,UAAU,KAAK,EAAE,MAAM,OAAO,EAAE,IAAI,SAAS,GAAG;AACrD,QAAI,OAAO,IAAI,IAAI,EAAE,QAAQ,GAAG;AAChC,QAAI,KAAK,EAAG,QAAO,EAAE,MAAM,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,GAAG,CAAC;AACnD,WAAO,EAAE,MAAM,GAAG,KAAK;AAAA,EACzB,CAAC;AACH;AACA,SAAS,SAAS,UAAU;AAC1B,SAAO,WAAW;AAChB,QAAI,KAAK,KAAK;AACd,QAAI,CAAC,GAAI;AACT,aAAS,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,QAAQ,GAAG,IAAI,GAAG,EAAE,GAAG;AACpD,UAAI,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,QAAQ,EAAE,SAAS,SAAS,SAAS,EAAE,SAAS,SAAS,MAAM;AACvF,aAAK,oBAAoB,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO;AAAA,MACxD,OAAO;AACL,WAAG,EAAE,CAAC,IAAI;AAAA,MACZ;AAAA,IACF;AACA,QAAI,EAAE,EAAG,IAAG,SAAS;AAAA,QAChB,QAAO,KAAK;AAAA,EACnB;AACF;AACA,SAAS,MAAM,UAAU,OAAO,SAAS;AACvC,SAAO,WAAW;AAChB,QAAI,KAAK,KAAK,MAAM,GAAG,WAAW,gBAAgB,KAAK;AACvD,QAAI,GAAI,UAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,IAAI,GAAG,EAAE,GAAG;AACjD,WAAK,IAAI,GAAG,CAAC,GAAG,SAAS,SAAS,QAAQ,EAAE,SAAS,SAAS,MAAM;AAClE,aAAK,oBAAoB,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO;AACtD,aAAK,iBAAiB,EAAE,MAAM,EAAE,WAAW,UAAU,EAAE,UAAU,OAAO;AACxE,UAAE,QAAQ;AACV;AAAA,MACF;AAAA,IACF;AACA,SAAK,iBAAiB,SAAS,MAAM,UAAU,OAAO;AACtD,QAAI,EAAE,MAAM,SAAS,MAAM,MAAM,SAAS,MAAM,OAAO,UAAU,QAAQ;AACzE,QAAI,CAAC,GAAI,MAAK,OAAO,CAAC,CAAC;AAAA,QAClB,IAAG,KAAK,CAAC;AAAA,EAChB;AACF;AACA,SAAS,aAAa,UAAU,OAAO,SAAS;AAC9C,MAAI,YAAY,eAAe,WAAW,EAAE,GAAG,GAAG,IAAI,UAAU,QAAQ;AACxE,MAAI,UAAU,SAAS,GAAG;AACxB,QAAI,KAAK,KAAK,KAAK,EAAE;AACrB,QAAI,GAAI,UAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,GAAG,IAAI,GAAG,EAAE,GAAG;AACpD,WAAK,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG;AACjC,aAAK,IAAI,UAAU,CAAC,GAAG,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM;AAC3D,iBAAO,EAAE;AAAA,QACX;AAAA,MACF;AAAA,IACF;AACA;AAAA,EACF;AACA,OAAK,QAAQ,QAAQ;AACrB,OAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,MAAK,KAAK,GAAG,UAAU,CAAC,GAAG,OAAO,OAAO,CAAC;AAClE,SAAO;AACT;AACA,SAAS,cAAc,MAAM,MAAM,QAAQ;AACzC,MAAI,UAAU,YAAY,IAAI,GAAG,QAAQ,QAAQ;AACjD,MAAI,OAAO,UAAU,YAAY;AAC/B,YAAQ,IAAI,MAAM,MAAM,MAAM;AAAA,EAChC,OAAO;AACL,YAAQ,QAAQ,SAAS,YAAY,OAAO;AAC5C,QAAI,OAAQ,OAAM,UAAU,MAAM,OAAO,SAAS,OAAO,UAAU,GAAG,MAAM,SAAS,OAAO;AAAA,QACvF,OAAM,UAAU,MAAM,OAAO,KAAK;AAAA,EACzC;AACA,OAAK,cAAc,KAAK;AAC1B;AACA,SAAS,iBAAiB,MAAM,QAAQ;AACtC,SAAO,WAAW;AAChB,WAAO,cAAc,MAAM,MAAM,MAAM;AAAA,EACzC;AACF;AACA,SAAS,iBAAiB,MAAM,QAAQ;AACtC,SAAO,WAAW;AAChB,WAAO,cAAc,MAAM,MAAM,OAAO,MAAM,MAAM,SAAS,CAAC;AAAA,EAChE;AACF;AACA,SAAS,mBAAmB,MAAM,QAAQ;AACxC,SAAO,KAAK,MAAM,OAAO,WAAW,aAAa,mBAAmB,kBAAkB,MAAM,MAAM,CAAC;AACrG;AACA,UAAU,qBAAqB;AAC7B,WAAS,SAAS,KAAK,SAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AACpE,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAO,MAAM,CAAC,EAAG,OAAM;AAAA,IAC7B;AAAA,EACF;AACF;AACA,IAAI,OAAO,CAAC,IAAI;AAChB,SAAS,YAAY,QAAQ,SAAS;AACpC,OAAK,UAAU;AACf,OAAK,WAAW;AAClB;AACA,SAAS,YAAY;AACnB,SAAO,IAAI,YAAY,CAAC,CAAC,SAAS,eAAe,CAAC,GAAG,IAAI;AAC3D;AACA,SAAS,sBAAsB;AAC7B,SAAO;AACT;AACA,YAAY,YAAY,UAAU,YAAY;AAAA,EAC5C,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,WAAW;AAAA,EACX,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,UAAU;AAAA,EACV,SAAS;AAAA,EACT,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,UAAU;AAAA,EACV,CAAC,OAAO,QAAQ,GAAG;AACrB;AACA,SAAS,OAAO,WAAW;AACzB,SAAO,OAAO,cAAc,WAAW,IAAI,YAAY,CAAC,CAAC,SAAS,cAAc,SAAS,CAAC,CAAC,GAAG,CAAC,SAAS,eAAe,CAAC,IAAI,IAAI,YAAY,CAAC,CAAC,SAAS,CAAC,GAAG,IAAI;AACjK;AACA,SAAS,YAAY,OAAO;AAC1B,MAAI;AACJ,SAAO,eAAe,MAAM,YAAa,SAAQ;AACjD,SAAO;AACT;AACA,SAAS,QAAQ,OAAO,MAAM;AAC5B,UAAQ,YAAY,KAAK;AACzB,MAAI,SAAS,OAAQ,QAAO,MAAM;AAClC,MAAI,MAAM;AACR,QAAI,MAAM,KAAK,mBAAmB;AAClC,QAAI,IAAI,gBAAgB;AACtB,UAAI,QAAQ,IAAI,eAAe;AAC/B,YAAM,IAAI,MAAM,SAAS,MAAM,IAAI,MAAM;AACzC,cAAQ,MAAM,gBAAgB,KAAK,aAAa,EAAE,QAAQ,CAAC;AAC3D,aAAO,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,IAC1B;AACA,QAAI,KAAK,uBAAuB;AAC9B,UAAI,OAAO,KAAK,sBAAsB;AACtC,aAAO,CAAC,MAAM,UAAU,KAAK,OAAO,KAAK,YAAY,MAAM,UAAU,KAAK,MAAM,KAAK,SAAS;AAAA,IAChG;AAAA,EACF;AACA,SAAO,CAAC,MAAM,OAAO,MAAM,KAAK;AAClC;AACA,IAAM,oBAAoB,EAAE,SAAS,MAAM,SAAS,MAAM;AAC1D,SAAS,UAAU,OAAO;AACxB,QAAM,eAAe;AACrB,QAAM,yBAAyB;AACjC;AACA,SAAS,YAAY,MAAM;AACzB,MAAI,QAAQ,KAAK,SAAS,iBAAiB,aAAa,OAAO,IAAI,EAAE,GAAG,kBAAkB,WAAW,iBAAiB;AACtH,MAAI,mBAAmB,OAAO;AAC5B,eAAW,GAAG,oBAAoB,WAAW,iBAAiB;AAAA,EAChE,OAAO;AACL,UAAM,aAAa,MAAM,MAAM;AAC/B,UAAM,MAAM,gBAAgB;AAAA,EAC9B;AACF;AACA,SAAS,QAAQ,MAAM,SAAS;AAC9B,MAAI,QAAQ,KAAK,SAAS,iBAAiB,aAAa,OAAO,IAAI,EAAE,GAAG,kBAAkB,IAAI;AAC9F,MAAI,SAAS;AACX,eAAW,GAAG,cAAc,WAAW,iBAAiB;AACxD,eAAW,WAAW;AACpB,iBAAW,GAAG,cAAc,IAAI;AAAA,IAClC,GAAG,CAAC;AAAA,EACN;AACA,MAAI,mBAAmB,OAAO;AAC5B,eAAW,GAAG,oBAAoB,IAAI;AAAA,EACxC,OAAO;AACL,UAAM,MAAM,gBAAgB,MAAM;AAClC,WAAO,MAAM;AAAA,EACf;AACF;AACA,SAAS,OAAO,aAAa,SAAS,WAAW;AAC/C,cAAY,YAAY,QAAQ,YAAY;AAC5C,YAAU,cAAc;AAC1B;AACA,SAAS,OAAO,QAAQ,YAAY;AAClC,MAAI,YAAY,OAAO,OAAO,OAAO,SAAS;AAC9C,WAAS,OAAO,WAAY,WAAU,GAAG,IAAI,WAAW,GAAG;AAC3D,SAAO;AACT;AACA,SAAS,QAAQ;AACjB;AACA,IAAI,SAAS;AACb,IAAI,WAAW,IAAI;AACnB,IAAI,MAAM;AAAV,IAAiC,MAAM;AAAvC,IAA4F,MAAM;AAAlG,IAAwJ,QAAQ;AAAhK,IAAsL,eAAe,IAAI,OAAO,UAAU,GAAG,IAAI,GAAG,IAAI,GAAG,MAAM;AAAjP,IAAoP,eAAe,IAAI,OAAO,UAAU,GAAG,IAAI,GAAG,IAAI,GAAG,MAAM;AAA/S,IAAkT,gBAAgB,IAAI,OAAO,WAAW,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,MAAM;AAAtX,IAAyX,gBAAgB,IAAI,OAAO,WAAW,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,MAAM;AAA7b,IAAgc,eAAe,IAAI,OAAO,UAAU,GAAG,IAAI,GAAG,IAAI,GAAG,MAAM;AAA3f,IAA8f,gBAAgB,IAAI,OAAO,WAAW,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,MAAM;AAClkB,IAAI,QAAQ;AAAA,EACV,WAAW;AAAA,EACX,cAAc;AAAA,EACd,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,MAAM;AAAA,EACN,UAAU;AAAA,EACV,UAAU;AAAA,EACV,eAAe;AAAA,EACf,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA,EACb,aAAa;AAAA,EACb,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,WAAW;AAAA,EACX,MAAM;AAAA,EACN,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AAAA,EACN,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,UAAU;AAAA,EACV,eAAe;AAAA,EACf,WAAW;AAAA,EACX,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,sBAAsB;AAAA,EACtB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,eAAe;AAAA,EACf,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,WAAW;AAAA,EACX,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,kBAAkB;AAAA,EAClB,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,WAAW;AAAA,EACX,eAAe;AAAA,EACf,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,KAAK;AAAA,EACL,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,MAAM;AAAA,EACN,aAAa;AAAA,EACb,WAAW;AAAA,EACX,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,aAAa;AACf;AACA,OAAO,OAAO,OAAO;AAAA,EACnB,KAAK,UAAU;AACb,WAAO,OAAO,OAAO,IAAI,KAAK,YAAY,GAAG,MAAM,QAAQ;AAAA,EAC7D;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,IAAI,EAAE,YAAY;AAAA,EAChC;AAAA,EACA,KAAK;AAAA;AAAA,EAEL,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AACZ,CAAC;AACD,SAAS,kBAAkB;AACzB,SAAO,KAAK,IAAI,EAAE,UAAU;AAC9B;AACA,SAAS,mBAAmB;AAC1B,SAAO,KAAK,IAAI,EAAE,WAAW;AAC/B;AACA,SAAS,kBAAkB;AACzB,SAAO,WAAW,IAAI,EAAE,UAAU;AACpC;AACA,SAAS,kBAAkB;AACzB,SAAO,KAAK,IAAI,EAAE,UAAU;AAC9B;AACA,SAAS,MAAM,QAAQ;AACrB,MAAI,GAAG;AACP,YAAU,SAAS,IAAI,KAAK,EAAE,YAAY;AAC1C,UAAQ,IAAI,MAAM,KAAK,MAAM,MAAM,IAAI,EAAE,CAAC,EAAE,QAAQ,IAAI,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,MAAM,IAAI,KAAK,CAAC,IAAI,MAAM,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,MAAM,IAAI,OAAO,IAAI,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,MAAM,IAAI,OAAO,GAAG,IAAI,MAAM,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,OAAO,IAAI,OAAO,IAAI,IAAI,MAAM,GAAG,IAAI,SAAS,IAAI,aAAa,KAAK,MAAM,KAAK,IAAI,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,aAAa,KAAK,MAAM,KAAK,IAAI,IAAI,EAAE,CAAC,IAAI,MAAM,KAAK,EAAE,CAAC,IAAI,MAAM,KAAK,EAAE,CAAC,IAAI,MAAM,KAAK,CAAC,KAAK,IAAI,cAAc,KAAK,MAAM,KAAK,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,IAAI,cAAc,KAAK,MAAM,KAAK,KAAK,EAAE,CAAC,IAAI,MAAM,KAAK,EAAE,CAAC,IAAI,MAAM,KAAK,EAAE,CAAC,IAAI,MAAM,KAAK,EAAE,CAAC,CAAC,KAAK,IAAI,aAAa,KAAK,MAAM,KAAK,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,KAAK,CAAC,KAAK,IAAI,cAAc,KAAK,MAAM,KAAK,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,CAAC,IAAI,MAAM,eAAe,MAAM,IAAI,KAAK,MAAM,MAAM,CAAC,IAAI,WAAW,gBAAgB,IAAI,IAAI,KAAK,KAAK,KAAK,CAAC,IAAI;AAC7+B;AACA,SAAS,KAAK,GAAG;AACf,SAAO,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC;AACxD;AACA,SAAS,KAAK,GAAG,GAAG,GAAG,GAAG;AACxB,MAAI,KAAK,EAAG,KAAI,IAAI,IAAI;AACxB,SAAO,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;AAC3B;AACA,SAAS,WAAW,GAAG;AACrB,MAAI,EAAE,aAAa,OAAQ,KAAI,MAAM,CAAC;AACtC,MAAI,CAAC,EAAG,QAAO,IAAI,IAAI;AACvB,MAAI,EAAE,IAAI;AACV,SAAO,IAAI,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO;AACzC;AACA,SAAS,IAAI,GAAG,GAAG,GAAG,SAAS;AAC7B,SAAO,UAAU,WAAW,IAAI,WAAW,CAAC,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,WAAW,OAAO,IAAI,OAAO;AAChG;AACA,SAAS,IAAI,GAAG,GAAG,GAAG,SAAS;AAC7B,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAAC;AACV,OAAK,UAAU,CAAC;AAClB;AACA,OAAO,KAAK,KAAK,OAAO,OAAO;AAAA,EAC7B,SAAS,GAAG;AACV,QAAI,KAAK,OAAO,WAAW,KAAK,IAAI,UAAU,CAAC;AAC/C,WAAO,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,OAAO;AAAA,EACjE;AAAA,EACA,OAAO,GAAG;AACR,QAAI,KAAK,OAAO,SAAS,KAAK,IAAI,QAAQ,CAAC;AAC3C,WAAO,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,OAAO;AAAA,EACjE;AAAA,EACA,MAAM;AACJ,WAAO;AAAA,EACT;AAAA,EACA,QAAQ;AACN,WAAO,IAAI,IAAI,OAAO,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC,GAAG,OAAO,KAAK,OAAO,CAAC;AAAA,EACrF;AAAA,EACA,cAAc;AACZ,WAAO,QAAQ,KAAK,KAAK,KAAK,IAAI,UAAU,QAAQ,KAAK,KAAK,KAAK,IAAI,WAAW,QAAQ,KAAK,KAAK,KAAK,IAAI,WAAW,KAAK,KAAK,WAAW,KAAK,WAAW;AAAA,EAC/J;AAAA,EACA,KAAK;AAAA;AAAA,EAEL,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AACZ,CAAC,CAAC;AACF,SAAS,gBAAgB;AACvB,SAAO,IAAI,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC;AACpD;AACA,SAAS,iBAAiB;AACxB,SAAO,IAAI,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,KAAK,MAAM,KAAK,OAAO,IAAI,IAAI,KAAK,WAAW,GAAG,CAAC;AAC1G;AACA,SAAS,gBAAgB;AACvB,QAAM,IAAI,OAAO,KAAK,OAAO;AAC7B,SAAO,GAAG,MAAM,IAAI,SAAS,OAAO,GAAG,OAAO,KAAK,CAAC,CAAC,KAAK,OAAO,KAAK,CAAC,CAAC,KAAK,OAAO,KAAK,CAAC,CAAC,GAAG,MAAM,IAAI,MAAM,KAAK,CAAC,GAAG;AACzH;AACA,SAAS,OAAO,SAAS;AACvB,SAAO,MAAM,OAAO,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,OAAO,CAAC;AAC9D;AACA,SAAS,OAAO,OAAO;AACrB,SAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,KAAK,KAAK,CAAC,CAAC;AAC1D;AACA,SAAS,IAAI,OAAO;AAClB,UAAQ,OAAO,KAAK;AACpB,UAAQ,QAAQ,KAAK,MAAM,MAAM,MAAM,SAAS,EAAE;AACpD;AACA,SAAS,KAAK,GAAG,GAAG,GAAG,GAAG;AACxB,MAAI,KAAK,EAAG,KAAI,IAAI,IAAI;AAAA,WACf,KAAK,KAAK,KAAK,EAAG,KAAI,IAAI;AAAA,WAC1B,KAAK,EAAG,KAAI;AACrB,SAAO,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;AAC3B;AACA,SAAS,WAAW,GAAG;AACrB,MAAI,aAAa,IAAK,QAAO,IAAI,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO;AAC7D,MAAI,EAAE,aAAa,OAAQ,KAAI,MAAM,CAAC;AACtC,MAAI,CAAC,EAAG,QAAO,IAAI,IAAI;AACvB,MAAI,aAAa,IAAK,QAAO;AAC7B,MAAI,EAAE,IAAI;AACV,MAAI,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC,GAAG,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,KAAK,KAAK,MAAM,OAAO;AAC7I,MAAI,GAAG;AACL,QAAI,MAAM,IAAK,MAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,aAClC,MAAM,IAAK,MAAK,IAAI,KAAK,IAAI;AAAA,QACjC,MAAK,IAAI,KAAK,IAAI;AACvB,SAAK,IAAI,MAAM,MAAM,MAAM,IAAI,MAAM;AACrC,SAAK;AAAA,EACP,OAAO;AACL,QAAI,IAAI,KAAK,IAAI,IAAI,IAAI;AAAA,EAC3B;AACA,SAAO,IAAI,IAAI,GAAG,GAAG,GAAG,EAAE,OAAO;AACnC;AACA,SAAS,IAAI,GAAG,GAAG,GAAG,SAAS;AAC7B,SAAO,UAAU,WAAW,IAAI,WAAW,CAAC,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,WAAW,OAAO,IAAI,OAAO;AAChG;AACA,SAAS,IAAI,GAAG,GAAG,GAAG,SAAS;AAC7B,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAAC;AACV,OAAK,UAAU,CAAC;AAClB;AACA,OAAO,KAAK,KAAK,OAAO,OAAO;AAAA,EAC7B,SAAS,GAAG;AACV,QAAI,KAAK,OAAO,WAAW,KAAK,IAAI,UAAU,CAAC;AAC/C,WAAO,IAAI,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,GAAG,KAAK,OAAO;AAAA,EACzD;AAAA,EACA,OAAO,GAAG;AACR,QAAI,KAAK,OAAO,SAAS,KAAK,IAAI,QAAQ,CAAC;AAC3C,WAAO,IAAI,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,GAAG,KAAK,OAAO;AAAA,EACzD;AAAA,EACA,MAAM;AACJ,QAAI,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,KAAK,IAAI,MAAM,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,IAAI,KAAK,GAAG,IAAI,KAAK,GAAG,KAAK,KAAK,IAAI,MAAM,IAAI,IAAI,KAAK,GAAG,KAAK,IAAI,IAAI;AACpJ,WAAO,IAAI;AAAA,MACT,QAAQ,KAAK,MAAM,IAAI,MAAM,IAAI,KAAK,IAAI,EAAE;AAAA,MAC5C,QAAQ,GAAG,IAAI,EAAE;AAAA,MACjB,QAAQ,IAAI,MAAM,IAAI,MAAM,IAAI,KAAK,IAAI,EAAE;AAAA,MAC3C,KAAK;AAAA,IACP;AAAA,EACF;AAAA,EACA,QAAQ;AACN,WAAO,IAAI,IAAI,OAAO,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC,GAAG,OAAO,KAAK,OAAO,CAAC;AAAA,EACrF;AAAA,EACA,cAAc;AACZ,YAAQ,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,CAAC,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,WAAW,KAAK,WAAW;AAAA,EAChI;AAAA,EACA,YAAY;AACV,UAAM,IAAI,OAAO,KAAK,OAAO;AAC7B,WAAO,GAAG,MAAM,IAAI,SAAS,OAAO,GAAG,OAAO,KAAK,CAAC,CAAC,KAAK,OAAO,KAAK,CAAC,IAAI,GAAG,MAAM,OAAO,KAAK,CAAC,IAAI,GAAG,IAAI,MAAM,IAAI,MAAM,KAAK,CAAC,GAAG;AAAA,EACvI;AACF,CAAC,CAAC;AACF,SAAS,OAAO,OAAO;AACrB,WAAS,SAAS,KAAK;AACvB,SAAO,QAAQ,IAAI,QAAQ,MAAM;AACnC;AACA,SAAS,OAAO,OAAO;AACrB,SAAO,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC;AAC5C;AACA,SAAS,QAAQ,GAAG,IAAI,IAAI;AAC1B,UAAQ,IAAI,KAAK,MAAM,KAAK,MAAM,IAAI,KAAK,IAAI,MAAM,KAAK,IAAI,MAAM,MAAM,KAAK,OAAO,MAAM,KAAK,KAAK,MAAM;AAC9G;AACA,IAAM,aAAa,CAAC,MAAM,MAAM;AAChC,SAAS,OAAO,GAAG,GAAG;AACpB,SAAO,SAAS,GAAG;AACjB,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;AACA,SAAS,YAAY,GAAG,GAAG,GAAG;AAC5B,SAAO,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,IAAI,GAAG,SAAS,GAAG;AACxE,WAAO,KAAK,IAAI,IAAI,IAAI,GAAG,CAAC;AAAA,EAC9B;AACF;AACA,SAAS,MAAM,GAAG;AAChB,UAAQ,IAAI,CAAC,OAAO,IAAI,UAAU,SAAS,GAAG,GAAG;AAC/C,WAAO,IAAI,IAAI,YAAY,GAAG,GAAG,CAAC,IAAI,WAAW,MAAM,CAAC,IAAI,IAAI,CAAC;AAAA,EACnE;AACF;AACA,SAAS,QAAQ,GAAG,GAAG;AACrB,MAAI,IAAI,IAAI;AACZ,SAAO,IAAI,OAAO,GAAG,CAAC,IAAI,WAAW,MAAM,CAAC,IAAI,IAAI,CAAC;AACvD;AACA,IAAM,iBAAiB,SAAS,SAAS,GAAG;AAC1C,MAAI,SAAS,MAAM,CAAC;AACpB,WAAS,MAAM,QAAQ,KAAK;AAC1B,QAAI,IAAI,QAAQ,SAAS,IAAI,MAAM,GAAG,IAAI,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,IAAI,OAAO,OAAO,GAAG,IAAI,CAAC,GAAG,IAAI,OAAO,OAAO,GAAG,IAAI,CAAC,GAAG,UAAU,QAAQ,OAAO,SAAS,IAAI,OAAO;AACrK,WAAO,SAAS,GAAG;AACjB,aAAO,IAAI,EAAE,CAAC;AACd,aAAO,IAAI,EAAE,CAAC;AACd,aAAO,IAAI,EAAE,CAAC;AACd,aAAO,UAAU,QAAQ,CAAC;AAC1B,aAAO,SAAS;AAAA,IAClB;AAAA,EACF;AACA,QAAM,QAAQ;AACd,SAAO;AACT,EAAE,CAAC;AACH,SAAS,kBAAkB,GAAG,GAAG;AAC/B,SAAO,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG;AACjC,WAAO,KAAK,IAAI,KAAK,IAAI;AAAA,EAC3B;AACF;AACA,IAAI,MAAM;AAAV,IAAyD,MAAM,IAAI,OAAO,IAAI,QAAQ,GAAG;AACzF,SAAS,KAAK,GAAG;AACf,SAAO,WAAW;AAChB,WAAO;AAAA,EACT;AACF;AACA,SAAS,IAAI,GAAG;AACd,SAAO,SAAS,GAAG;AACjB,WAAO,EAAE,CAAC,IAAI;AAAA,EAChB;AACF;AACA,SAAS,kBAAkB,GAAG,GAAG;AAC/B,MAAI,KAAK,IAAI,YAAY,IAAI,YAAY,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC;AAC7E,MAAI,IAAI,IAAI,IAAI,IAAI;AACpB,UAAQ,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,IAAI,KAAK,CAAC,IAAI;AAC/C,SAAK,KAAK,GAAG,SAAS,IAAI;AACxB,WAAK,EAAE,MAAM,IAAI,EAAE;AACnB,UAAI,EAAE,CAAC,EAAG,GAAE,CAAC,KAAK;AAAA,UACb,GAAE,EAAE,CAAC,IAAI;AAAA,IAChB;AACA,SAAK,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,IAAI;AACjC,UAAI,EAAE,CAAC,EAAG,GAAE,CAAC,KAAK;AAAA,UACb,GAAE,EAAE,CAAC,IAAI;AAAA,IAChB,OAAO;AACL,QAAE,EAAE,CAAC,IAAI;AACT,QAAE,KAAK,EAAE,GAAG,GAAG,kBAAkB,IAAI,EAAE,EAAE,CAAC;AAAA,IAC5C;AACA,SAAK,IAAI;AAAA,EACX;AACA,MAAI,KAAK,EAAE,QAAQ;AACjB,SAAK,EAAE,MAAM,EAAE;AACf,QAAI,EAAE,CAAC,EAAG,GAAE,CAAC,KAAK;AAAA,QACb,GAAE,EAAE,CAAC,IAAI;AAAA,EAChB;AACA,SAAO,EAAE,SAAS,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,KAAK,IAAI,EAAE,QAAQ,SAAS,GAAG;AAC9E,aAAS,KAAK,GAAG,GAAG,KAAK,GAAG,EAAE,GAAI,IAAG,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;AAC1D,WAAO,EAAE,KAAK,EAAE;AAAA,EAClB;AACF;AACA,IAAI,UAAU,MAAM,KAAK;AACzB,IAAI,aAAa;AAAA,EACf,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AACV;AACA,SAAS,UAAU,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACnC,MAAI,QAAQ,QAAQ;AACpB,MAAI,SAAS,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,EAAG,MAAK,QAAQ,KAAK;AACzD,MAAI,QAAQ,IAAI,IAAI,IAAI,EAAG,MAAK,IAAI,OAAO,KAAK,IAAI;AACpD,MAAI,SAAS,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,EAAG,MAAK,QAAQ,KAAK,QAAQ,SAAS;AAC1E,MAAI,IAAI,IAAI,IAAI,EAAG,KAAI,CAAC,GAAG,IAAI,CAAC,GAAG,QAAQ,CAAC,OAAO,SAAS,CAAC;AAC7D,SAAO;AAAA,IACL,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,QAAQ,KAAK,MAAM,GAAG,CAAC,IAAI;AAAA,IAC3B,OAAO,KAAK,KAAK,KAAK,IAAI;AAAA,IAC1B;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAI;AACJ,SAAS,SAAS,OAAO;AACvB,QAAM,IAAI,KAAK,OAAO,cAAc,aAAa,YAAY,iBAAiB,QAAQ,EAAE;AACxF,SAAO,EAAE,aAAa,aAAa,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAC3E;AACA,SAAS,SAAS,OAAO;AACvB,MAAI,SAAS,KAAM,QAAO;AAC1B,MAAI,CAAC,QAAS,WAAU,SAAS,gBAAgB,8BAA8B,GAAG;AAClF,UAAQ,aAAa,aAAa,KAAK;AACvC,MAAI,EAAE,QAAQ,QAAQ,UAAU,QAAQ,YAAY,GAAI,QAAO;AAC/D,UAAQ,MAAM;AACd,SAAO,UAAU,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AACvE;AACA,SAAS,qBAAqB,OAAO,SAAS,SAAS,UAAU;AAC/D,WAAS,IAAI,GAAG;AACd,WAAO,EAAE,SAAS,EAAE,IAAI,IAAI,MAAM;AAAA,EACpC;AACA,WAAS,UAAU,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG;AACvC,QAAI,OAAO,MAAM,OAAO,IAAI;AAC1B,UAAI,IAAI,EAAE,KAAK,cAAc,MAAM,SAAS,MAAM,OAAO;AACzD,QAAE,KAAK,EAAE,GAAG,IAAI,GAAG,GAAG,kBAAkB,IAAI,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,GAAG,GAAG,kBAAkB,IAAI,EAAE,EAAE,CAAC;AAAA,IAC/F,WAAW,MAAM,IAAI;AACnB,QAAE,KAAK,eAAe,KAAK,UAAU,KAAK,OAAO;AAAA,IACnD;AAAA,EACF;AACA,WAAS,OAAO,GAAG,GAAG,GAAG,GAAG;AAC1B,QAAI,MAAM,GAAG;AACX,UAAI,IAAI,IAAI,IAAK,MAAK;AAAA,eACb,IAAI,IAAI,IAAK,MAAK;AAC3B,QAAE,KAAK,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC,IAAI,WAAW,MAAM,QAAQ,IAAI,GAAG,GAAG,kBAAkB,GAAG,CAAC,EAAE,CAAC;AAAA,IAC1F,WAAW,GAAG;AACZ,QAAE,KAAK,IAAI,CAAC,IAAI,YAAY,IAAI,QAAQ;AAAA,IAC1C;AAAA,EACF;AACA,WAAS,MAAM,GAAG,GAAG,GAAG,GAAG;AACzB,QAAI,MAAM,GAAG;AACX,QAAE,KAAK,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC,IAAI,UAAU,MAAM,QAAQ,IAAI,GAAG,GAAG,kBAAkB,GAAG,CAAC,EAAE,CAAC;AAAA,IACzF,WAAW,GAAG;AACZ,QAAE,KAAK,IAAI,CAAC,IAAI,WAAW,IAAI,QAAQ;AAAA,IACzC;AAAA,EACF;AACA,WAAS,MAAM,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG;AACnC,QAAI,OAAO,MAAM,OAAO,IAAI;AAC1B,UAAI,IAAI,EAAE,KAAK,IAAI,CAAC,IAAI,UAAU,MAAM,KAAK,MAAM,GAAG;AACtD,QAAE,KAAK,EAAE,GAAG,IAAI,GAAG,GAAG,kBAAkB,IAAI,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,GAAG,GAAG,kBAAkB,IAAI,EAAE,EAAE,CAAC;AAAA,IAC/F,WAAW,OAAO,KAAK,OAAO,GAAG;AAC/B,QAAE,KAAK,IAAI,CAAC,IAAI,WAAW,KAAK,MAAM,KAAK,GAAG;AAAA,IAChD;AAAA,EACF;AACA,SAAO,SAAS,GAAG,GAAG;AACpB,QAAI,IAAI,CAAC,GAAG,IAAI,CAAC;AACjB,QAAI,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC;AACzB,cAAU,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,GAAG,CAAC;AACtE,WAAO,EAAE,QAAQ,EAAE,QAAQ,GAAG,CAAC;AAC/B,UAAM,EAAE,OAAO,EAAE,OAAO,GAAG,CAAC;AAC5B,UAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,GAAG,CAAC;AAClD,QAAI,IAAI;AACR,WAAO,SAAS,GAAG;AACjB,UAAI,IAAI,IAAI,IAAI,EAAE,QAAQ;AAC1B,aAAO,EAAE,IAAI,EAAG,IAAG,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;AACvC,aAAO,EAAE,KAAK,EAAE;AAAA,IAClB;AAAA,EACF;AACF;AACA,IAAI,0BAA0B,qBAAqB,UAAU,QAAQ,OAAO,MAAM;AAClF,IAAI,0BAA0B,qBAAqB,UAAU,MAAM,KAAK,GAAG;AAC3E,IAAI,WAAW;AACf,SAAS,KAAK,GAAG;AACf,WAAS,IAAI,KAAK,IAAI,CAAC,KAAK,IAAI,KAAK;AACvC;AACA,SAAS,KAAK,GAAG;AACf,WAAS,IAAI,KAAK,IAAI,CAAC,KAAK,IAAI,KAAK;AACvC;AACA,SAAS,KAAK,GAAG;AACf,WAAS,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,MAAM,IAAI;AAC5C;AACA,IAAM,kBAAkB,SAAS,QAAQ,KAAK,MAAM,MAAM;AACxD,WAAS,MAAM,IAAI,IAAI;AACrB,QAAI,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,GAAG;AAC3I,QAAI,KAAK,UAAU;AACjB,UAAI,KAAK,IAAI,KAAK,EAAE,IAAI;AACxB,UAAI,SAAS,GAAG;AACd,eAAO;AAAA,UACL,MAAM,IAAI;AAAA,UACV,MAAM,IAAI;AAAA,UACV,KAAK,KAAK,IAAI,MAAM,IAAI,CAAC;AAAA,QAC3B;AAAA,MACF;AAAA,IACF,OAAO;AACL,UAAI,KAAK,KAAK,KAAK,EAAE,GAAG,MAAM,KAAK,KAAK,KAAK,KAAK,OAAO,OAAO,IAAI,KAAK,OAAO,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,OAAO,OAAO,IAAI,KAAK,OAAO,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,CAAC,IAAI,EAAE,GAAG,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,CAAC,IAAI,EAAE;AACvO,WAAK,KAAK,MAAM;AAChB,UAAI,SAAS,GAAG;AACd,YAAI,IAAI,IAAI,GAAG,SAAS,KAAK,EAAE,GAAG,IAAI,MAAM,OAAO,OAAO,SAAS,KAAK,MAAM,IAAI,EAAE,IAAI,KAAK,EAAE;AAC/F,eAAO;AAAA,UACL,MAAM,IAAI;AAAA,UACV,MAAM,IAAI;AAAA,UACV,KAAK,SAAS,KAAK,MAAM,IAAI,EAAE;AAAA,QACjC;AAAA,MACF;AAAA,IACF;AACA,MAAE,WAAW,IAAI,MAAM,MAAM,KAAK;AAClC,WAAO;AAAA,EACT;AACA,QAAM,MAAM,SAAS,GAAG;AACtB,QAAI,KAAK,KAAK,IAAI,MAAM,CAAC,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,KAAK;AACrD,WAAO,QAAQ,IAAI,IAAI,EAAE;AAAA,EAC3B;AACA,SAAO;AACT,EAAE,KAAK,OAAO,GAAG,CAAC;AAClB,IAAI,QAAQ;AAAZ,IAAe,YAAY;AAA3B,IAA8B,WAAW;AAAzC,IAA4C,YAAY;AAAxD,IAA6D;AAA7D,IAAuE;AAAvE,IAAiF,YAAY;AAA7F,IAAgG,WAAW;AAA3G,IAA8G,YAAY;AAA1H,IAA6H,QAAQ,OAAO,gBAAgB,YAAY,YAAY,MAAM,cAAc;AAAxM,IAA8M,WAAW,OAAO,WAAW,YAAY,OAAO,wBAAwB,OAAO,sBAAsB,KAAK,MAAM,IAAI,SAAS,GAAG;AAC5U,aAAW,GAAG,EAAE;AAClB;AACA,SAAS,MAAM;AACb,SAAO,aAAa,SAAS,QAAQ,GAAG,WAAW,MAAM,IAAI,IAAI;AACnE;AACA,SAAS,WAAW;AAClB,aAAW;AACb;AACA,SAAS,QAAQ;AACf,OAAK,QAAQ,KAAK,QAAQ,KAAK,QAAQ;AACzC;AACA,MAAM,YAAY,MAAM,YAAY;AAAA,EAClC,aAAa;AAAA,EACb,SAAS,SAAS,UAAU,OAAO,MAAM;AACvC,QAAI,OAAO,aAAa,WAAY,OAAM,IAAI,UAAU,4BAA4B;AACpF,YAAQ,QAAQ,OAAO,IAAI,IAAI,CAAC,SAAS,SAAS,OAAO,IAAI,CAAC;AAC9D,QAAI,CAAC,KAAK,SAAS,aAAa,MAAM;AACpC,UAAI,SAAU,UAAS,QAAQ;AAAA,UAC1B,YAAW;AAChB,iBAAW;AAAA,IACb;AACA,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,UAAM;AAAA,EACR;AAAA,EACA,MAAM,WAAW;AACf,QAAI,KAAK,OAAO;AACd,WAAK,QAAQ;AACb,WAAK,QAAQ;AACb,YAAM;AAAA,IACR;AAAA,EACF;AACF;AACA,SAAS,MAAM,UAAU,OAAO,MAAM;AACpC,MAAI,IAAI,IAAI,MAAM;AAClB,IAAE,QAAQ,UAAU,OAAO,IAAI;AAC/B,SAAO;AACT;AACA,SAAS,aAAa;AACpB,MAAI;AACJ,IAAE;AACF,MAAI,IAAI,UAAU;AAClB,SAAO,GAAG;AACR,SAAK,IAAI,WAAW,EAAE,UAAU,EAAG,GAAE,MAAM,KAAK,QAAQ,CAAC;AACzD,QAAI,EAAE;AAAA,EACR;AACA,IAAE;AACJ;AACA,SAAS,OAAO;AACd,cAAY,YAAY,MAAM,IAAI,KAAK;AACvC,UAAQ,YAAY;AACpB,MAAI;AACF,eAAW;AAAA,EACb,UAAE;AACA,YAAQ;AACR,QAAI;AACJ,eAAW;AAAA,EACb;AACF;AACA,SAAS,OAAO;AACd,MAAI,OAAO,MAAM,IAAI,GAAG,QAAQ,OAAO;AACvC,MAAI,QAAQ,UAAW,cAAa,OAAO,YAAY;AACzD;AACA,SAAS,MAAM;AACb,MAAI,IAAI,KAAK,UAAU,IAAI,OAAO;AAClC,SAAO,IAAI;AACT,QAAI,GAAG,OAAO;AACZ,UAAI,OAAO,GAAG,MAAO,QAAO,GAAG;AAC/B,WAAK,IAAI,KAAK,GAAG;AAAA,IACnB,OAAO;AACL,WAAK,GAAG,OAAO,GAAG,QAAQ;AAC1B,WAAK,KAAK,GAAG,QAAQ,KAAK,WAAW;AAAA,IACvC;AAAA,EACF;AACA,aAAW;AACX,QAAM,IAAI;AACZ;AACA,SAAS,MAAM,MAAM;AACnB,MAAI,MAAO;AACX,MAAI,UAAW,aAAY,aAAa,SAAS;AACjD,MAAI,QAAQ,OAAO;AACnB,MAAI,QAAQ,IAAI;AACd,QAAI,OAAO,SAAU,aAAY,WAAW,MAAM,OAAO,MAAM,IAAI,IAAI,SAAS;AAChF,QAAI,SAAU,YAAW,cAAc,QAAQ;AAAA,EACjD,OAAO;AACL,QAAI,CAAC,SAAU,aAAY,MAAM,IAAI,GAAG,WAAW,YAAY,MAAM,SAAS;AAC9E,YAAQ,GAAG,SAAS,IAAI;AAAA,EAC1B;AACF;AACA,SAAS,QAAQ,UAAU,OAAO,MAAM;AACtC,MAAI,IAAI,IAAI,MAAM;AAClB,UAAQ,SAAS,OAAO,IAAI,CAAC;AAC7B,IAAE,QAAQ,CAAC,YAAY;AACrB,MAAE,KAAK;AACP,aAAS,UAAU,KAAK;AAAA,EAC1B,GAAG,OAAO,IAAI;AACd,SAAO;AACT;AACA,IAAI,UAAU,SAAS,SAAS,OAAO,UAAU,WAAW;AAC5D,IAAI,aAAa,CAAC;AAClB,IAAI,UAAU;AACd,IAAI,YAAY;AAChB,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,SAAS,SAAS,MAAM,MAAM,KAAK,OAAO,OAAO,QAAQ;AACvD,MAAI,YAAY,KAAK;AACrB,MAAI,CAAC,UAAW,MAAK,eAAe,CAAC;AAAA,WAC5B,OAAO,UAAW;AAC3B,SAAO,MAAM,KAAK;AAAA,IAChB;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,MAAM,OAAO;AAAA,IACb,OAAO,OAAO;AAAA,IACd,UAAU,OAAO;AAAA,IACjB,MAAM,OAAO;AAAA,IACb,OAAO;AAAA,IACP,OAAO;AAAA,EACT,CAAC;AACH;AACA,SAAS,KAAK,MAAM,KAAK;AACvB,MAAI,YAAY,IAAI,MAAM,GAAG;AAC7B,MAAI,UAAU,QAAQ,QAAS,OAAM,IAAI,MAAM,6BAA6B;AAC5E,SAAO;AACT;AACA,SAAS,IAAI,MAAM,KAAK;AACtB,MAAI,YAAY,IAAI,MAAM,GAAG;AAC7B,MAAI,UAAU,QAAQ,QAAS,OAAM,IAAI,MAAM,2BAA2B;AAC1E,SAAO;AACT;AACA,SAAS,IAAI,MAAM,KAAK;AACtB,MAAI,YAAY,KAAK;AACrB,MAAI,CAAC,aAAa,EAAE,YAAY,UAAU,GAAG,GAAI,OAAM,IAAI,MAAM,sBAAsB;AACvF,SAAO;AACT;AACA,SAAS,OAAO,MAAM,KAAK,MAAM;AAC/B,MAAI,YAAY,KAAK,cAAc;AACnC,YAAU,GAAG,IAAI;AACjB,OAAK,QAAQ,MAAM,WAAW,GAAG,KAAK,IAAI;AAC1C,WAAS,UAAU,SAAS;AAC1B,SAAK,QAAQ;AACb,SAAK,MAAM,QAAQ,QAAQ,KAAK,OAAO,KAAK,IAAI;AAChD,QAAI,KAAK,SAAS,QAAS,QAAO,UAAU,KAAK,KAAK;AAAA,EACxD;AACA,WAAS,OAAO,SAAS;AACvB,QAAI,GAAG,GAAG,GAAG;AACb,QAAI,KAAK,UAAU,UAAW,QAAO,KAAK;AAC1C,SAAK,KAAK,WAAW;AACnB,UAAI,UAAU,CAAC;AACf,UAAI,EAAE,SAAS,KAAK,KAAM;AAC1B,UAAI,EAAE,UAAU,QAAS,QAAO,QAAQ,MAAM;AAC9C,UAAI,EAAE,UAAU,SAAS;AACvB,UAAE,QAAQ;AACV,UAAE,MAAM,KAAK;AACb,UAAE,GAAG,KAAK,aAAa,MAAM,KAAK,UAAU,EAAE,OAAO,EAAE,KAAK;AAC5D,eAAO,UAAU,CAAC;AAAA,MACpB,WAAW,CAAC,IAAI,KAAK;AACnB,UAAE,QAAQ;AACV,UAAE,MAAM,KAAK;AACb,UAAE,GAAG,KAAK,UAAU,MAAM,KAAK,UAAU,EAAE,OAAO,EAAE,KAAK;AACzD,eAAO,UAAU,CAAC;AAAA,MACpB;AAAA,IACF;AACA,YAAQ,WAAW;AACjB,UAAI,KAAK,UAAU,SAAS;AAC1B,aAAK,QAAQ;AACb,aAAK,MAAM,QAAQ,MAAM,KAAK,OAAO,KAAK,IAAI;AAC9C,aAAK,OAAO;AAAA,MACd;AAAA,IACF,CAAC;AACD,SAAK,QAAQ;AACb,SAAK,GAAG,KAAK,SAAS,MAAM,KAAK,UAAU,KAAK,OAAO,KAAK,KAAK;AACjE,QAAI,KAAK,UAAU,SAAU;AAC7B,SAAK,QAAQ;AACb,YAAQ,IAAI,MAAM,IAAI,KAAK,MAAM,MAAM;AACvC,SAAK,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG;AAC9B,UAAI,IAAI,KAAK,MAAM,CAAC,EAAE,MAAM,KAAK,MAAM,KAAK,UAAU,KAAK,OAAO,KAAK,KAAK,GAAG;AAC7E,cAAM,EAAE,CAAC,IAAI;AAAA,MACf;AAAA,IACF;AACA,UAAM,SAAS,IAAI;AAAA,EACrB;AACA,WAAS,KAAK,SAAS;AACrB,QAAI,IAAI,UAAU,KAAK,WAAW,KAAK,KAAK,KAAK,MAAM,UAAU,KAAK,QAAQ,KAAK,KAAK,MAAM,QAAQ,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,IAAI,IAAI,MAAM;AACxJ,WAAO,EAAE,IAAI,GAAG;AACd,YAAM,CAAC,EAAE,KAAK,MAAM,CAAC;AAAA,IACvB;AACA,QAAI,KAAK,UAAU,QAAQ;AACzB,WAAK,GAAG,KAAK,OAAO,MAAM,KAAK,UAAU,KAAK,OAAO,KAAK,KAAK;AAC/D,WAAK;AAAA,IACP;AAAA,EACF;AACA,WAAS,OAAO;AACd,SAAK,QAAQ;AACb,SAAK,MAAM,KAAK;AAChB,WAAO,UAAU,GAAG;AACpB,aAAS,KAAK,UAAW;AACzB,WAAO,KAAK;AAAA,EACd;AACF;AACA,SAAS,UAAU,MAAM,MAAM;AAC7B,MAAI,YAAY,KAAK,cAAc,WAAW,QAAQ,SAAS,MAAM;AACrE,MAAI,CAAC,UAAW;AAChB,SAAO,QAAQ,OAAO,OAAO,OAAO;AACpC,OAAK,KAAK,WAAW;AACnB,SAAK,YAAY,UAAU,CAAC,GAAG,SAAS,MAAM;AAC5C,eAAS;AACT;AAAA,IACF;AACA,aAAS,UAAU,QAAQ,YAAY,UAAU,QAAQ;AACzD,cAAU,QAAQ;AAClB,cAAU,MAAM,KAAK;AACrB,cAAU,GAAG,KAAK,SAAS,cAAc,UAAU,MAAM,KAAK,UAAU,UAAU,OAAO,UAAU,KAAK;AACxG,WAAO,UAAU,CAAC;AAAA,EACpB;AACA,MAAI,OAAQ,QAAO,KAAK;AAC1B;AACA,SAAS,oBAAoB,MAAM;AACjC,SAAO,KAAK,KAAK,WAAW;AAC1B,cAAU,MAAM,IAAI;AAAA,EACtB,CAAC;AACH;AACA,SAAS,YAAY,KAAK,MAAM;AAC9B,MAAI,QAAQ;AACZ,SAAO,WAAW;AAChB,QAAI,YAAY,IAAI,MAAM,GAAG,GAAG,QAAQ,UAAU;AAClD,QAAI,UAAU,QAAQ;AACpB,eAAS,SAAS;AAClB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC7C,YAAI,OAAO,CAAC,EAAE,SAAS,MAAM;AAC3B,mBAAS,OAAO,MAAM;AACtB,iBAAO,OAAO,GAAG,CAAC;AAClB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,cAAU,QAAQ;AAAA,EACpB;AACF;AACA,SAAS,cAAc,KAAK,MAAM,OAAO;AACvC,MAAI,QAAQ;AACZ,MAAI,OAAO,UAAU,WAAY,OAAM,IAAI,MAAM;AACjD,SAAO,WAAW;AAChB,QAAI,YAAY,IAAI,MAAM,GAAG,GAAG,QAAQ,UAAU;AAClD,QAAI,UAAU,QAAQ;AACpB,gBAAU,SAAS,OAAO,MAAM;AAChC,eAAS,IAAI,EAAE,MAAM,MAAM,GAAG,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AAClE,YAAI,OAAO,CAAC,EAAE,SAAS,MAAM;AAC3B,iBAAO,CAAC,IAAI;AACZ;AAAA,QACF;AAAA,MACF;AACA,UAAI,MAAM,EAAG,QAAO,KAAK,CAAC;AAAA,IAC5B;AACA,cAAU,QAAQ;AAAA,EACpB;AACF;AACA,SAAS,iBAAiB,MAAM,OAAO;AACrC,MAAI,MAAM,KAAK;AACf,UAAQ;AACR,MAAI,UAAU,SAAS,GAAG;AACxB,QAAI,QAAQ,IAAI,KAAK,KAAK,GAAG,GAAG,EAAE;AAClC,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/C,WAAK,IAAI,MAAM,CAAC,GAAG,SAAS,MAAM;AAChC,eAAO,EAAE;AAAA,MACX;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO,KAAK,MAAM,SAAS,OAAO,cAAc,eAAe,KAAK,MAAM,KAAK,CAAC;AAClF;AACA,SAAS,WAAW,YAAY,MAAM,OAAO;AAC3C,MAAI,MAAM,WAAW;AACrB,aAAW,KAAK,WAAW;AACzB,QAAI,YAAY,IAAI,MAAM,GAAG;AAC7B,KAAC,UAAU,UAAU,UAAU,QAAQ,CAAC,IAAI,IAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AAAA,EACjF,CAAC;AACD,SAAO,SAAS,MAAM;AACpB,WAAO,IAAI,MAAM,GAAG,EAAE,MAAM,IAAI;AAAA,EAClC;AACF;AACA,SAAS,YAAY,GAAG,GAAG;AACzB,MAAI;AACJ,UAAQ,OAAO,MAAM,WAAW,oBAAoB,aAAa,QAAQ,kBAAkB,IAAI,MAAM,CAAC,MAAM,IAAI,GAAG,kBAAkB,mBAAmB,GAAG,CAAC;AAC9J;AACA,SAAS,WAAW,MAAM;AACxB,SAAO,WAAW;AAChB,SAAK,gBAAgB,IAAI;AAAA,EAC3B;AACF;AACA,SAAS,aAAa,UAAU;AAC9B,SAAO,WAAW;AAChB,SAAK,kBAAkB,SAAS,OAAO,SAAS,KAAK;AAAA,EACvD;AACF;AACA,SAAS,aAAa,MAAM,cAAc,QAAQ;AAChD,MAAI,UAAU,UAAU,SAAS,IAAI;AACrC,SAAO,WAAW;AAChB,QAAI,UAAU,KAAK,aAAa,IAAI;AACpC,WAAO,YAAY,UAAU,OAAO,YAAY,WAAW,eAAe,eAAe,aAAa,WAAW,SAAS,MAAM;AAAA,EAClI;AACF;AACA,SAAS,eAAe,UAAU,cAAc,QAAQ;AACtD,MAAI,UAAU,UAAU,SAAS,IAAI;AACrC,SAAO,WAAW;AAChB,QAAI,UAAU,KAAK,eAAe,SAAS,OAAO,SAAS,KAAK;AAChE,WAAO,YAAY,UAAU,OAAO,YAAY,WAAW,eAAe,eAAe,aAAa,WAAW,SAAS,MAAM;AAAA,EAClI;AACF;AACA,SAAS,aAAa,MAAM,cAAc,OAAO;AAC/C,MAAI,UAAU,UAAU;AACxB,SAAO,WAAW;AAChB,QAAI,SAAS,SAAS,MAAM,IAAI,GAAG;AACnC,QAAI,UAAU,KAAM,QAAO,KAAK,KAAK,gBAAgB,IAAI;AACzD,cAAU,KAAK,aAAa,IAAI;AAChC,cAAU,SAAS;AACnB,WAAO,YAAY,UAAU,OAAO,YAAY,YAAY,YAAY,WAAW,gBAAgB,WAAW,SAAS,eAAe,aAAa,WAAW,SAAS,MAAM;AAAA,EAC/K;AACF;AACA,SAAS,eAAe,UAAU,cAAc,OAAO;AACrD,MAAI,UAAU,UAAU;AACxB,SAAO,WAAW;AAChB,QAAI,SAAS,SAAS,MAAM,IAAI,GAAG;AACnC,QAAI,UAAU,KAAM,QAAO,KAAK,KAAK,kBAAkB,SAAS,OAAO,SAAS,KAAK;AACrF,cAAU,KAAK,eAAe,SAAS,OAAO,SAAS,KAAK;AAC5D,cAAU,SAAS;AACnB,WAAO,YAAY,UAAU,OAAO,YAAY,YAAY,YAAY,WAAW,gBAAgB,WAAW,SAAS,eAAe,aAAa,WAAW,SAAS,MAAM;AAAA,EAC/K;AACF;AACA,SAAS,gBAAgB,MAAM,OAAO;AACpC,MAAI,WAAW,UAAU,IAAI,GAAG,IAAI,aAAa,cAAc,0BAA0B;AACzF,SAAO,KAAK,UAAU,MAAM,OAAO,UAAU,cAAc,SAAS,QAAQ,iBAAiB,cAAc,UAAU,GAAG,WAAW,MAAM,UAAU,MAAM,KAAK,CAAC,IAAI,SAAS,QAAQ,SAAS,QAAQ,eAAe,YAAY,QAAQ,KAAK,SAAS,QAAQ,iBAAiB,cAAc,UAAU,GAAG,KAAK,CAAC;AAClT;AACA,SAAS,gBAAgB,MAAM,GAAG;AAChC,SAAO,SAAS,GAAG;AACjB,SAAK,aAAa,MAAM,EAAE,KAAK,MAAM,CAAC,CAAC;AAAA,EACzC;AACF;AACA,SAAS,kBAAkB,UAAU,GAAG;AACtC,SAAO,SAAS,GAAG;AACjB,SAAK,eAAe,SAAS,OAAO,SAAS,OAAO,EAAE,KAAK,MAAM,CAAC,CAAC;AAAA,EACrE;AACF;AACA,SAAS,YAAY,UAAU,OAAO;AACpC,MAAI,IAAI;AACR,WAAS,QAAQ;AACf,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,MAAM,GAAI,OAAM,KAAK,MAAM,kBAAkB,UAAU,CAAC;AAC5D,WAAO;AAAA,EACT;AACA,QAAM,SAAS;AACf,SAAO;AACT;AACA,SAAS,UAAU,MAAM,OAAO;AAC9B,MAAI,IAAI;AACR,WAAS,QAAQ;AACf,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,MAAM,GAAI,OAAM,KAAK,MAAM,gBAAgB,MAAM,CAAC;AACtD,WAAO;AAAA,EACT;AACA,QAAM,SAAS;AACf,SAAO;AACT;AACA,SAAS,qBAAqB,MAAM,OAAO;AACzC,MAAI,MAAM,UAAU;AACpB,MAAI,UAAU,SAAS,EAAG,SAAQ,MAAM,KAAK,MAAM,GAAG,MAAM,IAAI;AAChE,MAAI,SAAS,KAAM,QAAO,KAAK,MAAM,KAAK,IAAI;AAC9C,MAAI,OAAO,UAAU,WAAY,OAAM,IAAI,MAAM;AACjD,MAAI,WAAW,UAAU,IAAI;AAC7B,SAAO,KAAK,MAAM,MAAM,SAAS,QAAQ,cAAc,WAAW,UAAU,KAAK,CAAC;AACpF;AACA,SAAS,cAAc,KAAK,OAAO;AACjC,SAAO,WAAW;AAChB,SAAK,MAAM,GAAG,EAAE,QAAQ,CAAC,MAAM,MAAM,MAAM,SAAS;AAAA,EACtD;AACF;AACA,SAAS,cAAc,KAAK,OAAO;AACjC,SAAO,QAAQ,CAAC,OAAO,WAAW;AAChC,SAAK,MAAM,GAAG,EAAE,QAAQ;AAAA,EAC1B;AACF;AACA,SAAS,iBAAiB,OAAO;AAC/B,MAAI,MAAM,KAAK;AACf,SAAO,UAAU,SAAS,KAAK,MAAM,OAAO,UAAU,aAAa,gBAAgB,eAAe,KAAK,KAAK,CAAC,IAAI,IAAI,KAAK,KAAK,GAAG,GAAG,EAAE;AACzI;AACA,SAAS,iBAAiB,KAAK,OAAO;AACpC,SAAO,WAAW;AAChB,QAAI,MAAM,GAAG,EAAE,WAAW,CAAC,MAAM,MAAM,MAAM,SAAS;AAAA,EACxD;AACF;AACA,SAAS,iBAAiB,KAAK,OAAO;AACpC,SAAO,QAAQ,CAAC,OAAO,WAAW;AAChC,QAAI,MAAM,GAAG,EAAE,WAAW;AAAA,EAC5B;AACF;AACA,SAAS,oBAAoB,OAAO;AAClC,MAAI,MAAM,KAAK;AACf,SAAO,UAAU,SAAS,KAAK,MAAM,OAAO,UAAU,aAAa,mBAAmB,kBAAkB,KAAK,KAAK,CAAC,IAAI,IAAI,KAAK,KAAK,GAAG,GAAG,EAAE;AAC/I;AACA,SAAS,aAAa,KAAK,OAAO;AAChC,MAAI,OAAO,UAAU,WAAY,OAAM,IAAI,MAAM;AACjD,SAAO,WAAW;AAChB,QAAI,MAAM,GAAG,EAAE,OAAO;AAAA,EACxB;AACF;AACA,SAAS,gBAAgB,OAAO;AAC9B,MAAI,MAAM,KAAK;AACf,SAAO,UAAU,SAAS,KAAK,KAAK,aAAa,KAAK,KAAK,CAAC,IAAI,IAAI,KAAK,KAAK,GAAG,GAAG,EAAE;AACxF;AACA,SAAS,YAAY,KAAK,OAAO;AAC/B,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,OAAO,MAAM,WAAY,OAAM,IAAI,MAAM;AAC7C,QAAI,MAAM,GAAG,EAAE,OAAO;AAAA,EACxB;AACF;AACA,SAAS,uBAAuB,OAAO;AACrC,MAAI,OAAO,UAAU,WAAY,OAAM,IAAI,MAAM;AACjD,SAAO,KAAK,KAAK,YAAY,KAAK,KAAK,KAAK,CAAC;AAC/C;AACA,SAAS,kBAAkB,OAAO;AAChC,MAAI,OAAO,UAAU,WAAY,SAAQ,QAAQ,KAAK;AACtD,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,YAAY,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC9F,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,WAAW,UAAU,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACnG,WAAK,OAAO,MAAM,CAAC,MAAM,MAAM,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,GAAG;AAClE,iBAAS,KAAK,IAAI;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI,WAAW,WAAW,KAAK,UAAU,KAAK,OAAO,KAAK,GAAG;AACtE;AACA,SAAS,iBAAiB,YAAY;AACpC,MAAI,WAAW,QAAQ,KAAK,IAAK,OAAM,IAAI,MAAM;AACjD,WAAS,UAAU,KAAK,SAAS,UAAU,WAAW,SAAS,KAAK,QAAQ,QAAQ,KAAK,QAAQ,QAAQ,IAAI,KAAK,IAAI,IAAI,EAAE,GAAG,SAAS,IAAI,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACxK,aAAS,SAAS,QAAQ,CAAC,GAAG,SAAS,QAAQ,CAAC,GAAG,IAAI,OAAO,QAAQ,QAAQ,OAAO,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/H,UAAI,OAAO,OAAO,CAAC,KAAK,OAAO,CAAC,GAAG;AACjC,cAAM,CAAC,IAAI;AAAA,MACb;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI,IAAI,EAAE,GAAG;AAClB,WAAO,CAAC,IAAI,QAAQ,CAAC;AAAA,EACvB;AACA,SAAO,IAAI,WAAW,QAAQ,KAAK,UAAU,KAAK,OAAO,KAAK,GAAG;AACnE;AACA,SAAS,MAAM,MAAM;AACnB,UAAQ,OAAO,IAAI,KAAK,EAAE,MAAM,OAAO,EAAE,MAAM,SAAS,GAAG;AACzD,QAAI,IAAI,EAAE,QAAQ,GAAG;AACrB,QAAI,KAAK,EAAG,KAAI,EAAE,MAAM,GAAG,CAAC;AAC5B,WAAO,CAAC,KAAK,MAAM;AAAA,EACrB,CAAC;AACH;AACA,SAAS,WAAW,KAAK,MAAM,UAAU;AACvC,MAAI,KAAK,KAAK,MAAM,MAAM,IAAI,IAAI,OAAO;AACzC,SAAO,WAAW;AAChB,QAAI,YAAY,IAAI,MAAM,GAAG,GAAG,KAAK,UAAU;AAC/C,QAAI,OAAO,IAAK,EAAC,OAAO,MAAM,IAAI,KAAK,GAAG,GAAG,MAAM,QAAQ;AAC3D,cAAU,KAAK;AAAA,EACjB;AACF;AACA,SAAS,cAAc,MAAM,UAAU;AACrC,MAAI,MAAM,KAAK;AACf,SAAO,UAAU,SAAS,IAAI,IAAI,KAAK,KAAK,GAAG,GAAG,EAAE,GAAG,GAAG,IAAI,IAAI,KAAK,KAAK,WAAW,KAAK,MAAM,QAAQ,CAAC;AAC7G;AACA,SAAS,eAAe,KAAK;AAC3B,SAAO,WAAW;AAChB,QAAI,SAAS,KAAK;AAClB,aAAS,KAAK,KAAK,aAAc,KAAI,CAAC,MAAM,IAAK;AACjD,QAAI,OAAQ,QAAO,YAAY,IAAI;AAAA,EACrC;AACF;AACA,SAAS,oBAAoB;AAC3B,SAAO,KAAK,GAAG,cAAc,eAAe,KAAK,GAAG,CAAC;AACvD;AACA,SAAS,kBAAkB,SAAS;AAClC,MAAI,OAAO,KAAK,OAAO,MAAM,KAAK;AAClC,MAAI,OAAO,YAAY,WAAY,WAAU,SAAS,OAAO;AAC7D,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,YAAY,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC9F,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,WAAW,UAAU,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,MAAM,SAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtH,WAAK,OAAO,MAAM,CAAC,OAAO,UAAU,QAAQ,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,IAAI;AAChF,YAAI,cAAc,KAAM,SAAQ,WAAW,KAAK;AAChD,iBAAS,CAAC,IAAI;AACd,iBAAS,SAAS,CAAC,GAAG,MAAM,KAAK,GAAG,UAAU,IAAI,MAAM,GAAG,CAAC;AAAA,MAC9D;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI,WAAW,WAAW,KAAK,UAAU,MAAM,GAAG;AAC3D;AACA,SAAS,qBAAqB,SAAS;AACrC,MAAI,OAAO,KAAK,OAAO,MAAM,KAAK;AAClC,MAAI,OAAO,YAAY,WAAY,WAAU,YAAY,OAAO;AAChE,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,YAAY,CAAC,GAAG,UAAU,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAClG,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAO,MAAM,CAAC,GAAG;AACnB,iBAAS,YAAY,QAAQ,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,GAAG,OAAO,WAAW,IAAI,MAAM,GAAG,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC3I,cAAI,QAAQ,UAAU,CAAC,GAAG;AACxB,qBAAS,OAAO,MAAM,KAAK,GAAG,WAAW,QAAQ;AAAA,UACnD;AAAA,QACF;AACA,kBAAU,KAAK,SAAS;AACxB,gBAAQ,KAAK,IAAI;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI,WAAW,WAAW,SAAS,MAAM,GAAG;AACrD;AACA,IAAI,YAAY,UAAU,UAAU;AACpC,SAAS,uBAAuB;AAC9B,SAAO,IAAI,UAAU,KAAK,SAAS,KAAK,QAAQ;AAClD;AACA,SAAS,UAAU,MAAM,cAAc;AACrC,MAAI,UAAU,UAAU;AACxB,SAAO,WAAW;AAChB,QAAI,UAAU,WAAW,MAAM,IAAI,GAAG,WAAW,KAAK,MAAM,eAAe,IAAI,GAAG,WAAW,MAAM,IAAI;AACvG,WAAO,YAAY,UAAU,OAAO,YAAY,YAAY,YAAY,WAAW,eAAe,eAAe,aAAa,WAAW,SAAS,WAAW,OAAO;AAAA,EACtK;AACF;AACA,SAAS,YAAY,MAAM;AACzB,SAAO,WAAW;AAChB,SAAK,MAAM,eAAe,IAAI;AAAA,EAChC;AACF;AACA,SAAS,cAAc,MAAM,cAAc,QAAQ;AACjD,MAAI,UAAU,UAAU,SAAS,IAAI;AACrC,SAAO,WAAW;AAChB,QAAI,UAAU,WAAW,MAAM,IAAI;AACnC,WAAO,YAAY,UAAU,OAAO,YAAY,WAAW,eAAe,eAAe,aAAa,WAAW,SAAS,MAAM;AAAA,EAClI;AACF;AACA,SAAS,cAAc,MAAM,cAAc,OAAO;AAChD,MAAI,UAAU,UAAU;AACxB,SAAO,WAAW;AAChB,QAAI,UAAU,WAAW,MAAM,IAAI,GAAG,SAAS,MAAM,IAAI,GAAG,UAAU,SAAS;AAC/E,QAAI,UAAU,KAAM,WAAU,UAAU,KAAK,MAAM,eAAe,IAAI,GAAG,WAAW,MAAM,IAAI;AAC9F,WAAO,YAAY,UAAU,OAAO,YAAY,YAAY,YAAY,WAAW,gBAAgB,WAAW,SAAS,eAAe,aAAa,WAAW,SAAS,MAAM;AAAA,EAC/K;AACF;AACA,SAAS,iBAAiB,KAAK,MAAM;AACnC,MAAI,KAAK,KAAK,WAAW,MAAM,WAAW,MAAM,QAAQ,SAAS,KAAK;AACtE,SAAO,WAAW;AAChB,QAAI,YAAY,IAAI,MAAM,GAAG,GAAG,KAAK,UAAU,IAAI,WAAW,UAAU,MAAM,GAAG,KAAK,OAAO,YAAY,UAAU,YAAY,IAAI,KAAK;AACxI,QAAI,OAAO,OAAO,cAAc,SAAU,EAAC,OAAO,MAAM,IAAI,KAAK,GAAG,GAAG,OAAO,YAAY,QAAQ;AAClG,cAAU,KAAK;AAAA,EACjB;AACF;AACA,SAAS,iBAAiB,MAAM,OAAO,UAAU;AAC/C,MAAI,KAAK,QAAQ,QAAQ,cAAc,0BAA0B;AACjE,SAAO,SAAS,OAAO,KAAK,WAAW,MAAM,UAAU,MAAM,CAAC,CAAC,EAAE,GAAG,eAAe,MAAM,YAAY,IAAI,CAAC,IAAI,OAAO,UAAU,aAAa,KAAK,WAAW,MAAM,cAAc,MAAM,GAAG,WAAW,MAAM,WAAW,MAAM,KAAK,CAAC,CAAC,EAAE,KAAK,iBAAiB,KAAK,KAAK,IAAI,CAAC,IAAI,KAAK,WAAW,MAAM,cAAc,MAAM,GAAG,KAAK,GAAG,QAAQ,EAAE,GAAG,eAAe,MAAM,IAAI;AAC1W;AACA,SAAS,iBAAiB,MAAM,GAAG,UAAU;AAC3C,SAAO,SAAS,GAAG;AACjB,SAAK,MAAM,YAAY,MAAM,EAAE,KAAK,MAAM,CAAC,GAAG,QAAQ;AAAA,EACxD;AACF;AACA,SAAS,WAAW,MAAM,OAAO,UAAU;AACzC,MAAI,GAAG;AACP,WAAS,QAAQ;AACf,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,MAAM,GAAI,MAAK,KAAK,MAAM,iBAAiB,MAAM,GAAG,QAAQ;AAChE,WAAO;AAAA,EACT;AACA,QAAM,SAAS;AACf,SAAO;AACT;AACA,SAAS,sBAAsB,MAAM,OAAO,UAAU;AACpD,MAAI,MAAM,YAAY,QAAQ;AAC9B,MAAI,UAAU,SAAS,EAAG,SAAQ,MAAM,KAAK,MAAM,GAAG,MAAM,IAAI;AAChE,MAAI,SAAS,KAAM,QAAO,KAAK,MAAM,KAAK,IAAI;AAC9C,MAAI,OAAO,UAAU,WAAY,OAAM,IAAI,MAAM;AACjD,SAAO,KAAK,MAAM,KAAK,WAAW,MAAM,OAAO,YAAY,OAAO,KAAK,QAAQ,CAAC;AAClF;AACA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,SAAK,cAAc;AAAA,EACrB;AACF;AACA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,QAAI,SAAS,MAAM,IAAI;AACvB,SAAK,cAAc,UAAU,OAAO,KAAK;AAAA,EAC3C;AACF;AACA,SAAS,gBAAgB,OAAO;AAC9B,SAAO,KAAK,MAAM,QAAQ,OAAO,UAAU,aAAa,aAAa,WAAW,MAAM,QAAQ,KAAK,CAAC,IAAI,aAAa,SAAS,OAAO,KAAK,QAAQ,EAAE,CAAC;AACvJ;AACA,SAAS,gBAAgB,GAAG;AAC1B,SAAO,SAAS,GAAG;AACjB,SAAK,cAAc,EAAE,KAAK,MAAM,CAAC;AAAA,EACnC;AACF;AACA,SAAS,UAAU,OAAO;AACxB,MAAI,IAAI;AACR,WAAS,QAAQ;AACf,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,MAAM,GAAI,OAAM,KAAK,MAAM,gBAAgB,CAAC;AAChD,WAAO;AAAA,EACT;AACA,QAAM,SAAS;AACf,SAAO;AACT;AACA,SAAS,qBAAqB,OAAO;AACnC,MAAI,MAAM;AACV,MAAI,UAAU,SAAS,EAAG,SAAQ,MAAM,KAAK,MAAM,GAAG,MAAM,IAAI;AAChE,MAAI,SAAS,KAAM,QAAO,KAAK,MAAM,KAAK,IAAI;AAC9C,MAAI,OAAO,UAAU,WAAY,OAAM,IAAI,MAAM;AACjD,SAAO,KAAK,MAAM,KAAK,UAAU,KAAK,CAAC;AACzC;AACA,SAAS,wBAAwB;AAC/B,MAAI,OAAO,KAAK,OAAO,MAAM,KAAK,KAAK,MAAM,MAAM;AACnD,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACpE,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAO,MAAM,CAAC,GAAG;AACnB,YAAI,WAAW,IAAI,MAAM,GAAG;AAC5B,iBAAS,MAAM,MAAM,KAAK,GAAG,OAAO;AAAA,UAClC,MAAM,SAAS,OAAO,SAAS,QAAQ,SAAS;AAAA,UAChD,OAAO;AAAA,UACP,UAAU,SAAS;AAAA,UACnB,MAAM,SAAS;AAAA,QACjB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI,WAAW,QAAQ,KAAK,UAAU,MAAM,GAAG;AACxD;AACA,SAAS,iBAAiB;AACxB,MAAI,KAAK,KAAK,OAAO,MAAM,MAAM,KAAK,KAAK,OAAO,KAAK,KAAK;AAC5D,SAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAC3C,QAAI,SAAS,EAAE,OAAO,OAAO,GAAG,MAAM,EAAE,OAAO,WAAW;AACxD,UAAI,EAAE,SAAS,EAAG,SAAQ;AAAA,IAC5B,EAAE;AACF,SAAK,KAAK,WAAW;AACnB,UAAI,YAAY,IAAI,MAAM,GAAG,GAAG,KAAK,UAAU;AAC/C,UAAI,OAAO,KAAK;AACd,eAAO,MAAM,IAAI,KAAK;AACtB,YAAI,EAAE,OAAO,KAAK,MAAM;AACxB,YAAI,EAAE,UAAU,KAAK,MAAM;AAC3B,YAAI,EAAE,IAAI,KAAK,GAAG;AAAA,MACpB;AACA,gBAAU,KAAK;AAAA,IACjB,CAAC;AACD,QAAI,SAAS,EAAG,SAAQ;AAAA,EAC1B,CAAC;AACH;AACA,IAAI,KAAK;AACT,SAAS,WAAW,QAAQ,SAAS,MAAM,KAAK;AAC9C,OAAK,UAAU;AACf,OAAK,WAAW;AAChB,OAAK,QAAQ;AACb,OAAK,MAAM;AACb;AACA,SAAS,QAAQ;AACf,SAAO,EAAE;AACX;AACA,IAAI,sBAAsB,UAAU;AACpC,WAAW,YAAY;AAAA,EACrB,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,aAAa,oBAAoB;AAAA,EACjC,gBAAgB,oBAAoB;AAAA,EACpC,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,MAAM,oBAAoB;AAAA,EAC1B,OAAO,oBAAoB;AAAA,EAC3B,MAAM,oBAAoB;AAAA,EAC1B,MAAM,oBAAoB;AAAA,EAC1B,OAAO,oBAAoB;AAAA,EAC3B,MAAM,oBAAoB;AAAA,EAC1B,IAAI;AAAA,EACJ,MAAM;AAAA,EACN,WAAW;AAAA,EACX,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,UAAU;AAAA,EACV,MAAM;AAAA,EACN,aAAa;AAAA,EACb,KAAK;AAAA,EACL,CAAC,OAAO,QAAQ,GAAG,oBAAoB,OAAO,QAAQ;AACxD;AACA,SAAS,WAAW,GAAG;AACrB,WAAS,KAAK,MAAM,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK;AAC9D;AACA,IAAI,gBAAgB;AAAA,EAClB,MAAM;AAAA;AAAA,EAEN,OAAO;AAAA,EACP,UAAU;AAAA,EACV,MAAM;AACR;AACA,SAAS,QAAQ,MAAM,KAAK;AAC1B,MAAI;AACJ,SAAO,EAAE,SAAS,KAAK,iBAAiB,EAAE,SAAS,OAAO,GAAG,IAAI;AAC/D,QAAI,EAAE,OAAO,KAAK,aAAa;AAC7B,YAAM,IAAI,MAAM,cAAc,GAAG,YAAY;AAAA,IAC/C;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,MAAM;AAClC,MAAI,KAAK;AACT,MAAI,gBAAgB,YAAY;AAC9B,UAAM,KAAK,KAAK,OAAO,KAAK;AAAA,EAC9B,OAAO;AACL,UAAM,MAAM,IAAI,SAAS,eAAe,OAAO,IAAI,GAAG,OAAO,QAAQ,OAAO,OAAO,OAAO;AAAA,EAC5F;AACA,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACpE,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAO,MAAM,CAAC,GAAG;AACnB,iBAAS,MAAM,MAAM,KAAK,GAAG,OAAO,UAAU,QAAQ,MAAM,GAAG,CAAC;AAAA,MAClE;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI,WAAW,QAAQ,KAAK,UAAU,MAAM,GAAG;AACxD;AACA,UAAU,UAAU,YAAY;AAChC,UAAU,UAAU,aAAa;AACjC,IAAM,WAAW,CAAC,MAAM,MAAM;AAC9B,SAAS,UAAU,MAAM;AAAA,EACvB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA,UAAU;AACZ,GAAG;AACD,SAAO,iBAAiB,MAAM;AAAA,IAC5B,MAAM,EAAE,OAAO,MAAM,YAAY,MAAM,cAAc,KAAK;AAAA,IAC1D,aAAa,EAAE,OAAO,cAAc,YAAY,MAAM,cAAc,KAAK;AAAA,IACzE,QAAQ,EAAE,OAAO,QAAQ,YAAY,MAAM,cAAc,KAAK;AAAA,IAC9D,WAAW,EAAE,OAAO,WAAW,YAAY,MAAM,cAAc,KAAK;AAAA,IACpE,GAAG,EAAE,OAAO,UAAU;AAAA,EACxB,CAAC;AACH;AACA,SAAS,UAAU,GAAG,GAAG,GAAG;AAC1B,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI;AACX;AACA,UAAU,YAAY;AAAA,EACpB,aAAa;AAAA,EACb,OAAO,SAAS,GAAG;AACjB,WAAO,MAAM,IAAI,OAAO,IAAI,UAAU,KAAK,IAAI,GAAG,KAAK,GAAG,KAAK,CAAC;AAAA,EAClE;AAAA,EACA,WAAW,SAAS,GAAG,GAAG;AACxB,WAAO,MAAM,IAAI,MAAM,IAAI,OAAO,IAAI,UAAU,KAAK,GAAG,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,CAAC;AAAA,EAClG;AAAA,EACA,OAAO,SAAS,OAAO;AACrB,WAAO,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,KAAK,IAAI,KAAK,CAAC;AAAA,EAChE;AAAA,EACA,QAAQ,SAAS,GAAG;AAClB,WAAO,IAAI,KAAK,IAAI,KAAK;AAAA,EAC3B;AAAA,EACA,QAAQ,SAAS,GAAG;AAClB,WAAO,IAAI,KAAK,IAAI,KAAK;AAAA,EAC3B;AAAA,EACA,QAAQ,SAAS,UAAU;AACzB,WAAO,EAAE,SAAS,CAAC,IAAI,KAAK,KAAK,KAAK,IAAI,SAAS,CAAC,IAAI,KAAK,KAAK,KAAK,CAAC;AAAA,EAC1E;AAAA,EACA,SAAS,SAAS,GAAG;AACnB,YAAQ,IAAI,KAAK,KAAK,KAAK;AAAA,EAC7B;AAAA,EACA,SAAS,SAAS,GAAG;AACnB,YAAQ,IAAI,KAAK,KAAK,KAAK;AAAA,EAC7B;AAAA,EACA,UAAU,SAAS,GAAG;AACpB,WAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,KAAK,SAAS,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AAAA,EAC3E;AAAA,EACA,UAAU,SAAS,GAAG;AACpB,WAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,KAAK,SAAS,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AAAA,EAC3E;AAAA,EACA,UAAU,WAAW;AACnB,WAAO,eAAe,KAAK,IAAI,MAAM,KAAK,IAAI,aAAa,KAAK,IAAI;AAAA,EACtE;AACF;AACA,IAAI,WAAW,IAAI,UAAU,GAAG,GAAG,CAAC;AACpC,UAAU;AACV,SAAS,cAAc,OAAO;AAC5B,QAAM,yBAAyB;AACjC;AACA,SAAS,QAAQ,OAAO;AACtB,QAAM,eAAe;AACrB,QAAM,yBAAyB;AACjC;AACA,SAAS,cAAc,OAAO;AAC5B,UAAQ,CAAC,MAAM,WAAW,MAAM,SAAS,YAAY,CAAC,MAAM;AAC9D;AACA,SAAS,gBAAgB;AACvB,MAAI,IAAI;AACR,MAAI,aAAa,YAAY;AAC3B,QAAI,EAAE,mBAAmB;AACzB,QAAI,EAAE,aAAa,SAAS,GAAG;AAC7B,UAAI,EAAE,QAAQ;AACd,aAAO,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC;AAAA,IACrD;AACA,WAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,MAAM,QAAQ,OAAO,EAAE,OAAO,QAAQ,KAAK,CAAC;AAAA,EACjE;AACA,SAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,aAAa,EAAE,YAAY,CAAC;AACjD;AACA,SAAS,mBAAmB;AAC1B,SAAO,KAAK,UAAU;AACxB;AACA,SAAS,kBAAkB,OAAO;AAChC,SAAO,CAAC,MAAM,UAAU,MAAM,cAAc,IAAI,OAAO,MAAM,YAAY,IAAI,SAAS,MAAM,UAAU,KAAK;AAC7G;AACA,SAAS,mBAAmB;AAC1B,SAAO,UAAU,kBAAkB,kBAAkB;AACvD;AACA,SAAS,iBAAiB,WAAW,QAAQ,iBAAiB;AAC5D,MAAI,MAAM,UAAU,QAAQ,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,gBAAgB,CAAC,EAAE,CAAC,GAAG,MAAM,UAAU,QAAQ,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,gBAAgB,CAAC,EAAE,CAAC,GAAG,MAAM,UAAU,QAAQ,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,gBAAgB,CAAC,EAAE,CAAC,GAAG,MAAM,UAAU,QAAQ,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,gBAAgB,CAAC,EAAE,CAAC;AAC7P,SAAO,UAAU;AAAA,IACf,MAAM,OAAO,MAAM,OAAO,IAAI,KAAK,IAAI,GAAG,GAAG,KAAK,KAAK,IAAI,GAAG,GAAG;AAAA,IACjE,MAAM,OAAO,MAAM,OAAO,IAAI,KAAK,IAAI,GAAG,GAAG,KAAK,KAAK,IAAI,GAAG,GAAG;AAAA,EACnE;AACF;AACA,SAAS,OAAO;AACd,MAAI,UAAU,eAAe,SAAS,eAAe,YAAY,kBAAkB,cAAc,mBAAmB,YAAY,kBAAkB,cAAc,CAAC,GAAG,QAAQ,GAAG,kBAAkB,CAAC,CAAC,WAAW,SAAS,GAAG,CAAC,UAAU,QAAQ,CAAC,GAAG,WAAW,KAAK,eAAe,iBAAiB,YAAY,SAAS,SAAS,QAAQ,KAAK,GAAG,eAAe,YAAY,aAAa,aAAa,KAAK,aAAa,KAAK,iBAAiB,GAAG,cAAc;AAC7b,WAAS,MAAM,YAAY;AACzB,eAAW,SAAS,UAAU,gBAAgB,EAAE,GAAG,cAAc,SAAS,EAAE,SAAS,MAAM,CAAC,EAAE,GAAG,kBAAkB,WAAW,EAAE,GAAG,iBAAiB,UAAU,EAAE,OAAO,SAAS,EAAE,GAAG,mBAAmB,YAAY,EAAE,GAAG,kBAAkB,UAAU,EAAE,GAAG,kCAAkC,UAAU,EAAE,MAAM,+BAA+B,eAAe;AAAA,EAC9V;AACA,QAAM,YAAY,SAAS,YAAY,WAAW,OAAO,OAAO;AAC9D,QAAI,aAAa,WAAW,YAAY,WAAW,UAAU,IAAI;AACjE,eAAW,SAAS,UAAU,gBAAgB;AAC9C,QAAI,eAAe,YAAY;AAC7B,gBAAU,YAAY,WAAW,OAAO,KAAK;AAAA,IAC/C,OAAO;AACL,iBAAW,UAAU,EAAE,KAAK,WAAW;AACrC,gBAAQ,MAAM,SAAS,EAAE,MAAM,KAAK,EAAE,MAAM,EAAE,KAAK,MAAM,OAAO,cAAc,aAAa,UAAU,MAAM,MAAM,SAAS,IAAI,SAAS,EAAE,IAAI;AAAA,MAC/I,CAAC;AAAA,IACH;AAAA,EACF;AACA,QAAM,UAAU,SAAS,YAAY,GAAG,GAAG,OAAO;AAChD,UAAM,QAAQ,YAAY,WAAW;AACnC,UAAI,KAAK,KAAK,OAAO,GAAG,KAAK,OAAO,MAAM,aAAa,EAAE,MAAM,MAAM,SAAS,IAAI;AAClF,aAAO,KAAK;AAAA,IACd,GAAG,GAAG,KAAK;AAAA,EACb;AACA,QAAM,UAAU,SAAS,YAAY,GAAG,GAAG,OAAO;AAChD,UAAM,UAAU,YAAY,WAAW;AACrC,UAAI,IAAI,OAAO,MAAM,MAAM,SAAS,GAAG,KAAK,KAAK,QAAQ,KAAK,KAAK,OAAO,SAAS,CAAC,IAAI,OAAO,MAAM,aAAa,EAAE,MAAM,MAAM,SAAS,IAAI,GAAG,KAAK,GAAG,OAAO,EAAE,GAAG,KAAK,OAAO,MAAM,aAAa,EAAE,MAAM,MAAM,SAAS,IAAI;AAC9N,aAAO,UAAU,UAAU,MAAM,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,GAAG,eAAe;AAAA,IACvE,GAAG,GAAG,KAAK;AAAA,EACb;AACA,QAAM,cAAc,SAAS,YAAY,GAAG,GAAG,OAAO;AACpD,UAAM,UAAU,YAAY,WAAW;AACrC,aAAO,UAAU,KAAK,OAAO;AAAA,QAC3B,OAAO,MAAM,aAAa,EAAE,MAAM,MAAM,SAAS,IAAI;AAAA,QACrD,OAAO,MAAM,aAAa,EAAE,MAAM,MAAM,SAAS,IAAI;AAAA,MACvD,GAAG,OAAO,MAAM,MAAM,SAAS,GAAG,eAAe;AAAA,IACnD,GAAG,MAAM,KAAK;AAAA,EAChB;AACA,QAAM,cAAc,SAAS,YAAY,GAAG,GAAG,GAAG,OAAO;AACvD,UAAM,UAAU,YAAY,WAAW;AACrC,UAAI,IAAI,OAAO,MAAM,MAAM,SAAS,GAAG,IAAI,KAAK,QAAQ,KAAK,KAAK,OAAO,SAAS,CAAC,IAAI,OAAO,MAAM,aAAa,EAAE,MAAM,MAAM,SAAS,IAAI;AAC5I,aAAO,UAAU,SAAS,UAAU,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;AAAA,QAC3D,OAAO,MAAM,aAAa,CAAC,EAAE,MAAM,MAAM,SAAS,IAAI,CAAC;AAAA,QACvD,OAAO,MAAM,aAAa,CAAC,EAAE,MAAM,MAAM,SAAS,IAAI,CAAC;AAAA,MACzD,GAAG,GAAG,eAAe;AAAA,IACvB,GAAG,GAAG,KAAK;AAAA,EACb;AACA,WAAS,MAAM,WAAW,GAAG;AAC3B,QAAI,KAAK,IAAI,YAAY,CAAC,GAAG,KAAK,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC;AACxD,WAAO,MAAM,UAAU,IAAI,YAAY,IAAI,UAAU,GAAG,UAAU,GAAG,UAAU,CAAC;AAAA,EAClF;AACA,WAAS,UAAU,WAAW,IAAI,IAAI;AACpC,QAAI,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,UAAU,GAAG,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,UAAU;AACnE,WAAO,MAAM,UAAU,KAAK,MAAM,UAAU,IAAI,YAAY,IAAI,UAAU,UAAU,GAAG,GAAG,CAAC;AAAA,EAC7F;AACA,WAAS,SAAS,SAAS;AACzB,WAAO,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;AAAA,EACtF;AACA,WAAS,UAAU,YAAY,WAAW,OAAO,OAAO;AACtD,eAAW,GAAG,cAAc,WAAW;AACrC,cAAQ,MAAM,SAAS,EAAE,MAAM,KAAK,EAAE,MAAM;AAAA,IAC9C,CAAC,EAAE,GAAG,2BAA2B,WAAW;AAC1C,cAAQ,MAAM,SAAS,EAAE,MAAM,KAAK,EAAE,IAAI;AAAA,IAC5C,CAAC,EAAE,MAAM,QAAQ,WAAW;AAC1B,UAAI,OAAO,MAAM,OAAO,WAAW,IAAI,QAAQ,MAAM,IAAI,EAAE,MAAM,KAAK,GAAG,IAAI,OAAO,MAAM,MAAM,IAAI,GAAG,IAAI,SAAS,OAAO,SAAS,CAAC,IAAI,OAAO,UAAU,aAAa,MAAM,MAAM,MAAM,IAAI,IAAI,OAAO,IAAI,KAAK,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,KAAK,QAAQ,IAAI,OAAO,cAAc,aAAa,UAAU,MAAM,MAAM,IAAI,IAAI,WAAW,IAAI,aAAa,EAAE,OAAO,CAAC,EAAE,OAAO,IAAI,EAAE,CAAC,GAAG,EAAE,OAAO,CAAC,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC;AACra,aAAO,SAAS,GAAG;AACjB,YAAI,MAAM,EAAG,KAAI;AAAA,aACZ;AACH,cAAI,IAAI,EAAE,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;AACzB,cAAI,IAAI,UAAU,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC;AAAA,QACvD;AACA,UAAE,KAAK,MAAM,CAAC;AAAA,MAChB;AAAA,IACF,CAAC;AAAA,EACH;AACA,WAAS,QAAQ,MAAM,MAAM,OAAO;AAClC,WAAO,CAAC,SAAS,KAAK,aAAa,IAAI,QAAQ,MAAM,IAAI;AAAA,EAC3D;AACA,WAAS,QAAQ,MAAM,MAAM;AAC3B,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,SAAK,SAAS,OAAO,MAAM,MAAM,IAAI;AACrC,SAAK,OAAO;AAAA,EACd;AACA,UAAQ,YAAY;AAAA,IAClB,OAAO,SAAS,OAAO;AACrB,UAAI,MAAO,MAAK,cAAc;AAC9B,aAAO;AAAA,IACT;AAAA,IACA,OAAO,WAAW;AAChB,UAAI,EAAE,KAAK,WAAW,GAAG;AACvB,aAAK,KAAK,YAAY;AACtB,aAAK,KAAK,OAAO;AAAA,MACnB;AACA,aAAO;AAAA,IACT;AAAA,IACA,MAAM,SAAS,KAAK,WAAW;AAC7B,UAAI,KAAK,SAAS,QAAQ,QAAS,MAAK,MAAM,CAAC,IAAI,UAAU,OAAO,KAAK,MAAM,CAAC,CAAC;AACjF,UAAI,KAAK,UAAU,QAAQ,QAAS,MAAK,OAAO,CAAC,IAAI,UAAU,OAAO,KAAK,OAAO,CAAC,CAAC;AACpF,UAAI,KAAK,UAAU,QAAQ,QAAS,MAAK,OAAO,CAAC,IAAI,UAAU,OAAO,KAAK,OAAO,CAAC,CAAC;AACpF,WAAK,KAAK,SAAS;AACnB,WAAK,KAAK,MAAM;AAChB,aAAO;AAAA,IACT;AAAA,IACA,KAAK,WAAW;AACd,UAAI,EAAE,KAAK,WAAW,GAAG;AACvB,eAAO,KAAK,KAAK;AACjB,aAAK,KAAK,KAAK;AAAA,MACjB;AACA,aAAO;AAAA,IACT;AAAA,IACA,MAAM,SAAS,MAAM;AACnB,UAAI,IAAI,OAAO,KAAK,IAAI,EAAE,MAAM;AAChC,gBAAU;AAAA,QACR;AAAA,QACA,KAAK;AAAA,QACL,IAAI,UAAU,MAAM;AAAA,UAClB,aAAa,KAAK;AAAA,UAClB,QAAQ;AAAA,UACR;AAAA,UACA,WAAW,KAAK,KAAK;AAAA,UACrB,UAAU;AAAA,QACZ,CAAC;AAAA,QACD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,WAAS,QAAQ,UAAU,MAAM;AAC/B,QAAI,CAAC,QAAQ,MAAM,MAAM,SAAS,EAAG;AACrC,QAAI,IAAI,QAAQ,MAAM,IAAI,EAAE,MAAM,KAAK,GAAG,IAAI,KAAK,QAAQ,IAAI,KAAK,IAAI,YAAY,CAAC,GAAG,KAAK,IAAI,YAAY,CAAC,GAAG,EAAE,IAAI,KAAK,IAAI,GAAG,YAAY,MAAM,MAAM,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,QAAQ,KAAK;AAC3L,QAAI,EAAE,OAAO;AACX,UAAI,EAAE,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG;AACpD,UAAE,MAAM,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC;AAAA,MACtC;AACA,mBAAa,EAAE,KAAK;AAAA,IACtB,WAAW,EAAE,MAAM,EAAG;AAAA,SACjB;AACH,QAAE,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AACzB,gBAAU,IAAI;AACd,QAAE,MAAM;AAAA,IACV;AACA,YAAQ,KAAK;AACb,MAAE,QAAQ,WAAW,YAAY,UAAU;AAC3C,MAAE,KAAK,SAAS,UAAU,UAAU,MAAM,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,EAAE,QAAQ,eAAe,CAAC;AACpG,aAAS,aAAa;AACpB,QAAE,QAAQ;AACV,QAAE,IAAI;AAAA,IACR;AAAA,EACF;AACA,WAAS,YAAY,UAAU,MAAM;AACnC,QAAI,eAAe,CAAC,QAAQ,MAAM,MAAM,SAAS,EAAG;AACpD,QAAI,gBAAgB,MAAM,eAAe,IAAI,QAAQ,MAAM,MAAM,IAAI,EAAE,MAAM,KAAK,GAAG,IAAI,OAAO,MAAM,IAAI,EAAE,GAAG,kBAAkB,YAAY,IAAI,EAAE,GAAG,gBAAgB,YAAY,IAAI,GAAG,IAAI,QAAQ,OAAO,aAAa,GAAG,KAAK,MAAM,SAAS,KAAK,MAAM;AAC3P,gBAAY,MAAM,IAAI;AACtB,kBAAc,KAAK;AACnB,MAAE,QAAQ,CAAC,GAAG,KAAK,OAAO,OAAO,CAAC,CAAC;AACnC,cAAU,IAAI;AACd,MAAE,MAAM;AACR,aAAS,WAAW,QAAQ;AAC1B,cAAQ,MAAM;AACd,UAAI,CAAC,EAAE,OAAO;AACZ,YAAI,KAAK,OAAO,UAAU,IAAI,KAAK,OAAO,UAAU;AACpD,UAAE,QAAQ,KAAK,KAAK,KAAK,KAAK;AAAA,MAChC;AACA,QAAE,MAAM,MAAM,EAAE,KAAK,SAAS,UAAU,UAAU,EAAE,KAAK,QAAQ,EAAE,MAAM,CAAC,IAAI,QAAQ,QAAQ,aAAa,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,EAAE,QAAQ,eAAe,CAAC;AAAA,IACvJ;AACA,aAAS,WAAW,QAAQ;AAC1B,QAAE,GAAG,+BAA+B,IAAI;AACxC,cAAQ,OAAO,MAAM,EAAE,KAAK;AAC5B,cAAQ,MAAM;AACd,QAAE,MAAM,MAAM,EAAE,IAAI;AAAA,IACtB;AAAA,EACF;AACA,WAAS,WAAW,UAAU,MAAM;AAClC,QAAI,CAAC,QAAQ,MAAM,MAAM,SAAS,EAAG;AACrC,QAAI,KAAK,KAAK,QAAQ,KAAK,QAAQ,MAAM,iBAAiB,MAAM,eAAe,CAAC,IAAI,OAAO,IAAI,GAAG,KAAK,GAAG,OAAO,EAAE,GAAG,KAAK,GAAG,KAAK,MAAM,WAAW,MAAM,IAAI,KAAK,UAAU,UAAU,MAAM,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,MAAM,MAAM,IAAI,GAAG,eAAe;AACxP,YAAQ,KAAK;AACb,QAAI,WAAW,EAAG,QAAO,IAAI,EAAE,WAAW,EAAE,SAAS,QAAQ,EAAE,KAAK,WAAW,IAAI,IAAI,KAAK;AAAA,QACvF,QAAO,IAAI,EAAE,KAAK,MAAM,WAAW,IAAI,IAAI,KAAK;AAAA,EACvD;AACA,WAAS,aAAa,UAAU,MAAM;AACpC,QAAI,CAAC,QAAQ,MAAM,MAAM,SAAS,EAAG;AACrC,QAAI,UAAU,MAAM,SAAS,IAAI,QAAQ,QAAQ,IAAI,QAAQ,MAAM,MAAM,MAAM,eAAe,WAAW,CAAC,EAAE,MAAM,KAAK,GAAG,SAAS,GAAG,GAAG;AACzI,kBAAc,KAAK;AACnB,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,UAAI,QAAQ,CAAC,GAAG,IAAI,QAAQ,GAAG,IAAI;AACnC,UAAI,CAAC,GAAG,KAAK,OAAO,OAAO,CAAC,GAAG,EAAE,UAAU;AAC3C,UAAI,CAAC,EAAE,OAAQ,GAAE,SAAS,GAAG,UAAU,MAAM,EAAE,OAAO,IAAI,CAAC,CAAC;AAAA,eACnD,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,EAAG,GAAE,SAAS,GAAG,EAAE,OAAO;AAAA,IACrE;AACA,QAAI,cAAe,iBAAgB,aAAa,aAAa;AAC7D,QAAI,SAAS;AACX,UAAI,EAAE,OAAO,EAAG,cAAa,EAAE,CAAC,GAAG,gBAAgB,WAAW,WAAW;AACvE,wBAAgB;AAAA,MAClB,GAAG,UAAU;AACb,gBAAU,IAAI;AACd,QAAE,MAAM;AAAA,IACV;AAAA,EACF;AACA,WAAS,WAAW,UAAU,MAAM;AAClC,QAAI,CAAC,KAAK,UAAW;AACrB,QAAI,IAAI,QAAQ,MAAM,IAAI,EAAE,MAAM,KAAK,GAAG,UAAU,MAAM,gBAAgB,IAAI,QAAQ,QAAQ,GAAG,GAAG,GAAG;AACvG,YAAQ,KAAK;AACb,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,UAAI,QAAQ,CAAC,GAAG,IAAI,QAAQ,GAAG,IAAI;AACnC,UAAI,EAAE,UAAU,EAAE,OAAO,CAAC,MAAM,EAAE,WAAY,GAAE,OAAO,CAAC,IAAI;AAAA,eACnD,EAAE,UAAU,EAAE,OAAO,CAAC,MAAM,EAAE,WAAY,GAAE,OAAO,CAAC,IAAI;AAAA,IACnE;AACA,QAAI,EAAE,KAAK;AACX,QAAI,EAAE,QAAQ;AACZ,UAAI,KAAK,EAAE,OAAO,CAAC,GAAG,KAAK,EAAE,OAAO,CAAC,GAAG,KAAK,EAAE,OAAO,CAAC,GAAG,KAAK,EAAE,OAAO,CAAC,GAAG,MAAM,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,IAAI,MAAM,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK;AAChM,UAAI,MAAM,GAAG,KAAK,KAAK,KAAK,EAAE,CAAC;AAC/B,UAAI,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC;AAC7C,UAAI,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC;AAAA,IAC/C,WAAW,EAAE,OAAQ,KAAI,EAAE,OAAO,CAAC,GAAG,IAAI,EAAE,OAAO,CAAC;AAAA,QAC/C;AACL,MAAE,KAAK,SAAS,UAAU,UAAU,GAAG,GAAG,CAAC,GAAG,EAAE,QAAQ,eAAe,CAAC;AAAA,EAC1E;AACA,WAAS,WAAW,UAAU,MAAM;AAClC,QAAI,CAAC,KAAK,UAAW;AACrB,QAAI,IAAI,QAAQ,MAAM,IAAI,EAAE,MAAM,KAAK,GAAG,UAAU,MAAM,gBAAgB,IAAI,QAAQ,QAAQ,GAAG;AACjG,kBAAc,KAAK;AACnB,QAAI,YAAa,cAAa,WAAW;AACzC,kBAAc,WAAW,WAAW;AAClC,oBAAc;AAAA,IAChB,GAAG,UAAU;AACb,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,UAAI,QAAQ,CAAC;AACb,UAAI,EAAE,UAAU,EAAE,OAAO,CAAC,MAAM,EAAE,WAAY,QAAO,EAAE;AAAA,eAC9C,EAAE,UAAU,EAAE,OAAO,CAAC,MAAM,EAAE,WAAY,QAAO,EAAE;AAAA,IAC9D;AACA,QAAI,EAAE,UAAU,CAAC,EAAE,OAAQ,GAAE,SAAS,EAAE,QAAQ,OAAO,EAAE;AACzD,QAAI,EAAE,OAAQ,GAAE,OAAO,CAAC,IAAI,KAAK,OAAO,OAAO,EAAE,OAAO,CAAC,CAAC;AAAA,SACrD;AACH,QAAE,IAAI;AACN,UAAI,EAAE,SAAS,GAAG;AAChB,YAAI,QAAQ,GAAG,IAAI;AACnB,YAAI,KAAK,MAAM,WAAW,CAAC,IAAI,EAAE,CAAC,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,aAAa;AACxE,cAAI,IAAI,OAAO,IAAI,EAAE,GAAG,eAAe;AACvC,cAAI,EAAG,GAAE,MAAM,MAAM,SAAS;AAAA,QAChC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,aAAa,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,cAAc,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAG,SAAS;AAAA,EAChG;AACA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,UAAU,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS;AAAA,EAC7F;AACA,QAAM,YAAY,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAU,YAAY,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS;AAAA,EAC/F;AACA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,SAAS,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS;AAAA,EACrI;AACA,QAAM,cAAc,SAAS,GAAG;AAC9B,WAAO,UAAU,UAAU,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AAAA,EACrH;AACA,QAAM,kBAAkB,SAAS,GAAG;AAClC,WAAO,UAAU,UAAU,gBAAgB,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA,EAC7Q;AACA,QAAM,YAAY,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAU,YAAY,GAAG,SAAS;AAAA,EACrD;AACA,QAAM,WAAW,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,WAAW,CAAC,GAAG,SAAS;AAAA,EACrD;AACA,QAAM,cAAc,SAAS,GAAG;AAC9B,WAAO,UAAU,UAAU,eAAe,GAAG,SAAS;AAAA,EACxD;AACA,QAAM,KAAK,WAAW;AACpB,QAAI,QAAQ,UAAU,GAAG,MAAM,WAAW,SAAS;AACnD,WAAO,UAAU,YAAY,QAAQ;AAAA,EACvC;AACA,QAAM,gBAAgB,SAAS,GAAG;AAChC,WAAO,UAAU,UAAU,kBAAkB,IAAI,CAAC,KAAK,GAAG,SAAS,KAAK,KAAK,cAAc;AAAA,EAC7F;AACA,QAAM,cAAc,SAAS,GAAG;AAC9B,WAAO,UAAU,UAAU,cAAc,CAAC,GAAG,SAAS;AAAA,EACxD;AACA,SAAO;AACT;AACA,IAAM,QAAQ,OAAO,cAAc;AACnC,IAAM,eAAe,CAAC,MAAM,KAAK,KAAK,MAAM,MAAM,SAAS,UAAU,QAAQ,UAAU,gBAAgB,iBAAiB;AACxH,IAAM,gBAAgB;AAAA,EACpB,MAAM;AAAA,EACN,cAAc,EAAE,MAAM,EAAE;AAAA,EACxB,cAAc;AAChB;AACA,IAAM,cAA8B,gBAAgB;AAAA,EAClD,GAAG;AAAA,EACH,OAAO;AAAA,IACL,IAAI,CAAC;AAAA,IACL,MAAM,CAAC;AAAA,IACP,UAAU,EAAE,MAAM,QAAQ;AAAA,IAC1B,UAAU,EAAE,MAAM,QAAQ;AAAA,IAC1B,UAAU,CAAC;AAAA,IACX,YAAY,CAAC;AAAA,IACb,cAAc,CAAC;AAAA,IACf,OAAO,CAAC;AAAA,IACR,gBAAgB,CAAC;AAAA,IACjB,aAAa,CAAC;AAAA,IACd,aAAa,CAAC;AAAA,IACd,QAAQ,EAAE,MAAM,QAAQ;AAAA,EAC1B;AAAA,EACA,OAAO,CAAC,SAAS,YAAY,cAAc,aAAa,YAAY;AAAA,EACpE,MAAM,SAAS,EAAE,MAAM,MAAM,GAAG;AAC9B,UAAM,QAAQ;AACd,UAAM,eAAe,OAAO,KAAK;AACjC,UAAM,QAAQ,SAAS;AACvB,UAAM,QAAQ,MAAM,MAAM,MAAM,SAAS,CAAC,CAAC;AAC3C,aAAS,QAAQ,OAAO;AACtB,YAAM,SAAS,KAAK;AAAA,IACtB;AACA,aAAS,WAAW,OAAO;AACzB,YAAM,YAAY,KAAK;AAAA,IACzB;AACA,aAAS,aAAa,OAAO;AAC3B,YAAM,cAAc,KAAK;AAAA,IAC3B;AACA,aAAS,YAAY,OAAO;AAC1B,YAAM,aAAa,KAAK;AAAA,IAC1B;AACA,aAAS,aAAa,OAAO;AAC3B,YAAM,cAAc,KAAK;AAAA,IAC3B;AACA,WAAO,CAAC,MAAM,WAAW;AACvB,aAAO,CAAC,KAAK,UAAU,KAAK,WAAW,UAAU,KAAK,KAAK,WAAW,WAAW,KAAK,UAAU,GAAG,mBAAmB,UAAU,EAAE,KAAK,EAAE,GAAG;AAAA,QAC1I,MAAM,YAAY,EAAE,QAAQ,MAAM,IAAI,EAAE,KAAK,UAAU,GAAG,YAAY,wBAAwB,MAAM,YAAY,EAAE,QAAQ,MAAM,IAAI,EAAE,CAAC,GAAG,eAAe,WAAW,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,OAAO,GAAG,KAAK,OAAO,CAAC,CAAC,GAAG,MAAM,EAAE,MAAM,UAAU,GAAG,mBAAmB,QAAQ,WAAW;AAAA,UAC/Q,KAAK;AAAA,UACL,IAAI,KAAK;AAAA,QACX,GAAG,KAAK,QAAQ;AAAA,UACd,OAAO,CAAC,0BAA0B,EAAE,UAAU,KAAK,UAAU,UAAU,KAAK,SAAS,CAAC;AAAA,UACtF,GAAG,KAAK,SAAS;AAAA,UACjB,GAAG,KAAK,SAAS;AAAA,UACjB,IAAI,KAAK;AAAA,UACT,IAAI,KAAK;AAAA,UACT,OAAO,KAAK,WAAW;AAAA,UACvB,QAAQ,KAAK,WAAW;AAAA,UACxB,MAAM,KAAK,SAAS,MAAM,MAAM,cAAc,MAAM,MAAM;AAAA,UAC1D,QAAQ,KAAK;AAAA,UACb,gBAAgB,KAAK;AAAA,UACrB,mBAAmB,KAAK;AAAA,UACxB;AAAA,UACA;AAAA,UACA,cAAc;AAAA,UACd,aAAa;AAAA,UACb,cAAc;AAAA,QAChB,CAAC,GAAG,MAAM,IAAI,YAAY;AAAA,MAC5B,GAAG,EAAE,KAAK,mBAAmB,IAAI,IAAI;AAAA,IACvC;AAAA,EACF;AACF,CAAC;AACD,IAAM,aAAa,CAAC,SAAS,UAAU,WAAW,iBAAiB;AACnE,IAAM,aAAa,CAAC,IAAI;AACxB,IAAM,aAAa,CAAC,KAAK,QAAQ,UAAU,cAAc;AACzD,IAAM,cAAc;AAAA,EAClB,MAAM;AAAA,EACN,cAAc,EAAE,MAAM,EAAE;AAC1B;AACA,IAAMA,aAA4B,gBAAgB;AAAA,EAChD,GAAG;AAAA,EACH,OAAO;AAAA,IACL,WAAW,EAAE,MAAM,CAAC,QAAQ,QAAQ,GAAG,SAAS,UAAU;AAAA,IAC1D,iBAAiB,EAAE,MAAM,CAAC,QAAQ,QAAQ,GAAG,SAAS,cAAc;AAAA,IACpE,eAAe,EAAE,MAAM,CAAC,QAAQ,QAAQ,EAAE;AAAA,IAC1C,kBAAkB,EAAE,SAAS,EAAE;AAAA,IAC/B,iBAAiB,EAAE,SAAS,EAAE;AAAA,IAC9B,WAAW,EAAE,SAAS,0BAA0B;AAAA,IAChD,iBAAiB,EAAE,SAAS,OAAO;AAAA,IACnC,iBAAiB,EAAE,SAAS,EAAE;AAAA,IAC9B,UAAU,EAAE,SAAS,eAAe;AAAA,IACpC,UAAU,EAAE,MAAM,SAAS,SAAS,MAAM;AAAA,IAC1C,UAAU,EAAE,MAAM,SAAS,SAAS,MAAM;AAAA,IAC1C,OAAO,CAAC;AAAA,IACR,QAAQ,CAAC;AAAA,IACT,WAAW,EAAE,SAAS,oBAAoB;AAAA,IAC1C,YAAY,EAAE,MAAM,SAAS,SAAS,MAAM;AAAA,IAC5C,UAAU,EAAE,SAAS,EAAE;AAAA,IACvB,aAAa,EAAE,SAAS,EAAE;AAAA,IAC1B,kBAAkB,EAAE,SAAS,EAAE;AAAA,EACjC;AAAA,EACA,OAAO,CAAC,SAAS,aAAa,gBAAgB,kBAAkB,iBAAiB,gBAAgB;AAAA,EACjG,MAAM,SAAS,EAAE,KAAK,GAAG;AACvB,UAAM,QAAQ,SAAS;AACvB,UAAM,QAAQ,SAAS;AACvB,UAAM,eAAe;AACrB,UAAM,gBAAgB;AACtB,UAAM,EAAE,IAAI,KAAK,OAAO,UAAU,iBAAiB,YAAY,OAAO,aAAa,QAAQ,oBAAoB,IAAI,WAAW;AAC9H,UAAM,KAAK,IAAI;AACf,YAAQ,OAAO,KAAK;AACpB,UAAM,eAAe,MAAM,MAAM;AAC/B,UAAI;AACJ,aAAO,QAAQ,WAAW,KAAK,MAAM,UAAU,OAAO,SAAS,GAAG,UAAU;AAAA,IAC9E,CAAC;AACD,UAAM,gBAAgB,MAAM,MAAM;AAChC,UAAI;AACJ,aAAO,QAAQ,YAAY,KAAK,MAAM,UAAU,OAAO,SAAS,GAAG,WAAW;AAAA,IAChF,CAAC;AACD,UAAM,iBAAiB,OAAO,WAAW,eAAe,CAAC,CAAC,OAAO,SAAS,eAAe;AACzF,UAAM,gBAAgB,SAAS,MAAM,OAAO,QAAQ,cAAc,WAAW,MAAM,QAAQ,YAAY,QAAQ,SAAS;AACxH,UAAM,sBAAsB;AAAA,MAC1B,MAAM,OAAO,QAAQ,oBAAoB,WAAW,MAAM,QAAQ,kBAAkB,QAAQ;AAAA,IAC9F;AACA,UAAM,oBAAoB;AAAA,MACxB,MAAM,OAAO,QAAQ,kBAAkB,WAAW,MAAM,QAAQ,gBAAgB,OAAO,QAAQ,kBAAkB,aAAa,QAAQ,gBAAgB,MAAM;AAAA,IAC9J;AACA,UAAM,KAAK,SAAS,MAAM,eAAe,oBAAoB,MAAM,OAAO,CAAC,SAAS,CAAC,KAAK,MAAM,CAAC,CAAC;AAClG,UAAM,SAAS,SAAS,OAAO;AAAA,MAC7B,GAAG,CAAC,SAAS,MAAM,IAAI,SAAS,MAAM;AAAA,MACtC,GAAG,CAAC,SAAS,MAAM,IAAI,SAAS,MAAM;AAAA,MACtC,OAAO,WAAW,MAAM,QAAQ,SAAS,MAAM;AAAA,MAC/C,QAAQ,WAAW,MAAM,SAAS,SAAS,MAAM;AAAA,IACnD,EAAE;AACF,UAAM,eAAe;AAAA,MACnB,MAAM,oBAAoB,SAAS,oBAAoB,MAAM,SAAS,iBAAiB,GAAG,OAAO,OAAO,KAAK,IAAI,OAAO;AAAA,IAC1H;AACA,UAAM,YAAY,SAAS,MAAM;AAC/B,YAAM,cAAc,aAAa,MAAM,QAAQ,aAAa;AAC5D,YAAM,eAAe,aAAa,MAAM,SAAS,cAAc;AAC/D,aAAO,KAAK,IAAI,aAAa,YAAY;AAAA,IAC3C,CAAC;AACD,UAAM,UAAU,SAAS,MAAM;AAC7B,YAAM,YAAY,UAAU,QAAQ,aAAa;AACjD,YAAM,aAAa,UAAU,QAAQ,cAAc;AACnD,YAAM,SAAS,QAAQ,cAAc,UAAU;AAC/C,aAAO;AAAA,QACL;AAAA,QACA,GAAG,aAAa,MAAM,KAAK,YAAY,aAAa,MAAM,SAAS,IAAI;AAAA,QACvE,GAAG,aAAa,MAAM,KAAK,aAAa,aAAa,MAAM,UAAU,IAAI;AAAA,QACzE,OAAO,YAAY,SAAS;AAAA,QAC5B,QAAQ,aAAa,SAAS;AAAA,MAChC;AAAA,IACF,CAAC;AACD,UAAM,IAAI,SAAS,MAAM;AACvB,UAAI,CAAC,QAAQ,MAAM,KAAK,CAAC,QAAQ,MAAM,GAAG;AACxC,eAAO;AAAA,MACT;AACA,aAAO;AAAA,OACN,QAAQ,MAAM,IAAI,QAAQ,MAAM,MAAM,IAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM,MAAM;AAAA,OAChF,QAAQ,MAAM,QAAQ,QAAQ,MAAM,SAAS,CAAC;AAAA,OAC9C,QAAQ,MAAM,SAAS,QAAQ,MAAM,SAAS,CAAC;AAAA,OAC/C,CAAC,QAAQ,MAAM,QAAQ,QAAQ,MAAM,SAAS,CAAC;AAAA,OAC/C,OAAO,MAAM,IAAI,QAAQ,gBAAgB,IAAI,OAAO,MAAM,CAAC;AAAA,OAC3D,OAAO,MAAM,QAAQ,IAAI,QAAQ,gBAAgB;AAAA,OACjD,QAAQ,gBAAgB,IAAI,QAAQ,gBAAgB,UAAU,QAAQ,gBAAgB,IAAI,QAAQ,gBAAgB;AAAA,OAClH,OAAO,MAAM,SAAS,IAAI,QAAQ,gBAAgB;AAAA,OAClD,QAAQ,gBAAgB,IAAI,QAAQ,gBAAgB,WAAW,QAAQ,gBAAgB,IAAI,QAAQ,gBAAgB;AAAA,OACnH,EAAE,OAAO,MAAM,QAAQ,IAAI,QAAQ,iBAAiB;AAAA,OACpD,QAAQ,gBAAgB,IAAI,QAAQ,gBAAgB,WAAW,QAAQ,gBAAgB,KAAK,QAAQ,gBAAgB;AAAA,OACpH,EAAE,OAAO,MAAM,SAAS,IAAI,QAAQ,iBAAiB;AAAA,OACrD,QAAQ,gBAAgB,IAAI,QAAQ,gBAAgB,UAAU,QAAQ,gBAAgB,KAAK,QAAQ,gBAAgB;AAAA,IACtH,CAAC;AACD;AAAA,MACE,CAAC,cAAc;AACb,YAAI,GAAG,OAAO;AACZ,gBAAM,aAAa,OAAO,GAAG,KAAK;AAClC,gBAAM,cAAc,CAAC,UAAU;AAC7B,gBAAI,MAAM,YAAY,SAAS,WAAW,CAAC,YAAY,SAAS,CAAC,OAAO,OAAO;AAC7E;AAAA,YACF;AACA,kBAAM,SAAS,MAAM,YAAY,WAAW,QAAQ,IAAI,KAAK;AAC7D,kBAAM,aAAa,CAAC,MAAM,YAAY,UAAU,MAAM,YAAY,cAAc,IAAI,OAAO,MAAM,YAAY,YAAY,IAAI,QAAQ,QAAQ;AAC7I,kBAAM,WAAW,SAAS,MAAM,OAAO,MAAM,aAAa;AAC1D,mBAAO,MAAM,QAAQ,YAAY,OAAO,QAAQ;AAAA,UAClD;AACA,gBAAM,aAAa,CAAC,UAAU;AAC5B,gBAAI,MAAM,YAAY,SAAS,eAAe,CAAC,YAAY,SAAS,CAAC,OAAO,OAAO;AACjF;AAAA,YACF;AACA,kBAAM,YAAY,UAAU,QAAQ,KAAK,IAAI,GAAG,SAAS,MAAM,IAAI,KAAK,QAAQ,aAAa,KAAK;AAClG,kBAAM,WAAW;AAAA,cACf,GAAG,SAAS,MAAM,IAAI,MAAM,YAAY,YAAY;AAAA,cACpD,GAAG,SAAS,MAAM,IAAI,MAAM,YAAY,YAAY;AAAA,YACtD;AACA,kBAAM,SAAS;AAAA,cACb,CAAC,GAAG,CAAC;AAAA,cACL,CAAC,WAAW,MAAM,OAAO,WAAW,MAAM,MAAM;AAAA,YAClD;AACA,kBAAM,gBAAgB,SAAS,UAAU,SAAS,GAAG,SAAS,CAAC,EAAE,MAAM,SAAS,MAAM,IAAI;AAC1F,kBAAM,uBAAuB,OAAO,MAAM,UAAU,EAAE,eAAe,QAAQ,gBAAgB,KAAK;AAClG,mBAAO,MAAM,UAAU,YAAY,OAAO,oBAAoB;AAAA,UAChE;AACA,gBAAM,oBAAoB,KAAK,EAAE,WAAW,CAAC,UAAU,WAAW,KAAK,KAAK,QAAQ,WAAW,GAAG,EAAE,GAAG,QAAQ,QAAQ,WAAW,aAAa,MAAM;AAAA,UACrJ,CAAC,EAAE,GAAG,cAAc,QAAQ,WAAW,cAAc,MAAM;AAAA,UAC3D,CAAC;AACD,qBAAW,KAAK,iBAAiB;AACjC,oBAAU,MAAM;AACd,uBAAW,GAAG,QAAQ,IAAI;AAAA,UAC5B,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,EAAE,OAAO,OAAO;AAAA,IAClB;AACA,aAAS,WAAW,OAAO;AACzB,YAAM,CAAC,GAAG,CAAC,IAAI,QAAQ,KAAK;AAC5B,WAAK,SAAS,EAAE,OAAO,UAAU,EAAE,GAAG,EAAE,EAAE,CAAC;AAAA,IAC7C;AACA,aAAS,YAAY,OAAO,MAAM;AAChC,YAAM,QAAQ,EAAE,OAAO,MAAM,gBAAgB,kBAAkB,CAAC,IAAI,GAAG,MAAM,KAAK,EAAE;AACpF,YAAM,iBAAiB,KAAK;AAC5B,WAAK,aAAa,KAAK;AAAA,IACzB;AACA,aAAS,eAAe,OAAO,MAAM;AACnC,YAAM,QAAQ,EAAE,OAAO,MAAM,gBAAgB,kBAAkB,CAAC,IAAI,GAAG,MAAM,KAAK,EAAE;AACpF,YAAM,uBAAuB,KAAK;AAClC,WAAK,gBAAgB,KAAK;AAAA,IAC5B;AACA,aAAS,iBAAiB,OAAO,MAAM;AACrC,YAAM,QAAQ,EAAE,OAAO,MAAM,gBAAgB,kBAAkB,CAAC,IAAI,GAAG,MAAM,KAAK,EAAE;AACpF,YAAM,sBAAsB,KAAK;AACjC,WAAK,kBAAkB,KAAK;AAAA,IAC9B;AACA,aAAS,gBAAgB,OAAO,MAAM;AACpC,YAAM,QAAQ,EAAE,OAAO,MAAM,gBAAgB,kBAAkB,CAAC,IAAI,GAAG,MAAM,KAAK,EAAE;AACpF,YAAM,qBAAqB,KAAK;AAChC,WAAK,iBAAiB,KAAK;AAAA,IAC7B;AACA,aAAS,iBAAiB,OAAO,MAAM;AACrC,YAAM,QAAQ,EAAE,OAAO,MAAM,gBAAgB,kBAAkB,CAAC,IAAI,GAAG,MAAM,KAAK,EAAE;AACpF,YAAM,sBAAsB,KAAK;AACjC,WAAK,kBAAkB,KAAK;AAAA,IAC9B;AACA,WAAO,CAAC,MAAM,WAAW;AACvB,aAAO,UAAU,GAAG,YAAY,MAAM,SAAK,GAAG;AAAA,QAC5C,UAAU,KAAK;AAAA,QACf,OAAO,eAAe,CAAC,qBAAqB,EAAE,UAAU,KAAK,UAAU,UAAU,KAAK,SAAS,CAAC,CAAC;AAAA,MACnG,GAAG;AAAA,QACD,SAAS,QAAQ,MAAM;AAAA,WACpB,UAAU,GAAG,mBAAmB,OAAO;AAAA,YACtC,SAAS;AAAA,YACT,KAAK;AAAA,YACL,OAAO,aAAa;AAAA,YACpB,QAAQ,cAAc;AAAA,YACtB,SAAS,CAAC,QAAQ,MAAM,GAAG,QAAQ,MAAM,GAAG,QAAQ,MAAM,OAAO,QAAQ,MAAM,MAAM,EAAE,KAAK,GAAG;AAAA,YAC/F,MAAM;AAAA,YACN,mBAAmB,qBAAqB,MAAM,GAAG,CAAC;AAAA,YAClD,SAAS;AAAA,UACX,GAAG;AAAA,YACD,KAAK,aAAa,UAAU,GAAG,mBAAmB,SAAS;AAAA,cACzD,KAAK;AAAA,cACL,IAAI,qBAAqB,MAAM,GAAG,CAAC;AAAA,YACrC,GAAG,gBAAgB,KAAK,SAAS,GAAG,GAAG,UAAU,KAAK,mBAAmB,IAAI,IAAI;AAAA,aAChF,UAAU,IAAI,GAAG,mBAAmB,UAAU,MAAM,WAAW,MAAM,mBAAmB,GAAG,CAAC,SAAS;AACpG,qBAAO,UAAU,GAAG,YAAY,aAAa;AAAA,gBAC3C,IAAI,KAAK;AAAA,gBACT,KAAK,KAAK;AAAA,gBACV,GAAG;AAAA,gBACH,UAAU,KAAK;AAAA,gBACf,YAAY,KAAK;AAAA,gBACjB,UAAU,KAAK;AAAA,gBACf,UAAU,KAAK;AAAA,gBACf,OAAO,eAAe,KAAK,KAAK;AAAA,gBAChC,OAAO,eAAe,kBAAkB,MAAM,IAAI,CAAC;AAAA,gBACnD,OAAO,cAAc,MAAM,IAAI;AAAA,gBAC/B,iBAAiB,KAAK;AAAA,gBACtB,gBAAgB,oBAAoB,MAAM,IAAI;AAAA,gBAC9C,gBAAgB,KAAK;AAAA,gBACrB,mBAAmB,MAAM,cAAc;AAAA,gBACvC,MAAM,KAAK;AAAA,gBACX,QAAQ,KAAK;AAAA,gBACb,SAAS,CAAC,WAAW,YAAY,QAAQ,IAAI;AAAA,gBAC7C,YAAY,CAAC,WAAW,eAAe,QAAQ,IAAI;AAAA,gBACnD,cAAc,CAAC,WAAW,iBAAiB,QAAQ,IAAI;AAAA,gBACvD,aAAa,CAAC,WAAW,gBAAgB,QAAQ,IAAI;AAAA,gBACrD,cAAc,CAAC,WAAW,iBAAiB,QAAQ,IAAI;AAAA,cACzD,GAAG,MAAM,GAAG,CAAC,MAAM,YAAY,cAAc,YAAY,YAAY,SAAS,SAAS,SAAS,iBAAiB,gBAAgB,gBAAgB,mBAAmB,QAAQ,UAAU,WAAW,cAAc,gBAAgB,eAAe,cAAc,CAAC;AAAA,YAC/P,CAAC,GAAG,GAAG;AAAA,YACP,gBAAmB,QAAQ;AAAA,cACzB,OAAO;AAAA,cACP,GAAG,EAAE;AAAA,cACL,MAAM,KAAK;AAAA,cACX,QAAQ,KAAK;AAAA,cACb,gBAAgB,KAAK;AAAA,cACrB,aAAa;AAAA,YACf,GAAG,MAAM,GAAG,UAAU;AAAA,UACxB,GAAG,GAAG,UAAU;AAAA,QAClB,CAAC;AAAA,QACD,GAAG;AAAA,MACL,GAAG,GAAG,CAAC,YAAY,OAAO,CAAC;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;", "names": ["_sfc_main"]}
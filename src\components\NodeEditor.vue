<template>
  <div class="node-editor" v-if="selectedNode">
    <div class="editor-header">
      <h3>Edit Node</h3>
      <Button 
        icon="pi pi-times" 
        text 
        @click="closeEditor"
        class="p-button-sm"
      />
    </div>
    
    <div class="editor-content">
      <TabView>
        <TabPanel header="Basic">
          <div class="form-group">
            <label for="nodeId">Node ID</label>
            <InputText 
              id="nodeId"
              v-model="editData.Id" 
              placeholder="Optional unique identifier"
            />
          </div>
          
          <div class="form-group">
            <label for="nodeText">Dialogue Text *</label>
            <Textarea 
              id="nodeText"
              v-model="editData.Text" 
              rows="4"
              placeholder="Enter the dialogue text..."
              :class="{ 'p-invalid': !editData.Text }"
            />
          </div>
          
          <div class="form-group">
            <label for="animation">Animation</label>
            <InputText 
              id="animation"
              v-model="editData.Animation" 
              placeholder="Animation ID"
            />
          </div>
          
          <div class="form-group">
            <label for="sound">Sound</label>
            <InputText 
              id="sound"
              v-model="editData.Sound" 
              placeholder="Sound ID"
            />
          </div>
          
          <div class="form-group">
            <label for="exit">Exit Button Text</label>
            <InputText 
              id="exit"
              v-model="editData.Exit" 
              placeholder="Text for exit button (leave empty to disable)"
            />
          </div>
        </TabPanel>
        
        <TabPanel header="Options">
          <div class="options-section">
            <div class="section-header">
              <h4>Player Options</h4>
              <Button 
                icon="pi pi-plus" 
                label="Add Option"
                size="small"
                @click="addOption"
              />
            </div>
            
            <div v-if="editData.Options?.length" class="options-list">
              <Card v-for="(option, index) in editData.Options" :key="index" class="option-card">
                <template #content>
                  <div class="option-header">
                    <span class="option-number">{{ index + 1 }}</span>
                    <Button 
                      icon="pi pi-trash" 
                      text 
                      severity="danger"
                      size="small"
                      @click="removeOption(index)"
                    />
                  </div>
                  
                  <div class="form-group">
                    <label>Option Text *</label>
                    <InputText 
                      v-model="option.Text" 
                      placeholder="What the player can choose"
                      :class="{ 'p-invalid': !option.Text }"
                    />
                  </div>
                  
                  <div class="form-group">
                    <label>Option Type</label>
                    <Dropdown 
                      v-model="option.Type" 
                      :options="optionTypes"
                      option-label="label"
                      option-value="value"
                      placeholder="Select option type"
                    />
                  </div>
                  
                  <div v-if="option.Type === 'EVENT'" class="form-group">
                    <label>Event Type</label>
                    <Dropdown 
                      v-model="option.Argument.Type" 
                      :options="eventTypes"
                      option-label="label"
                      option-value="value"
                      placeholder="Select event type"
                    />
                  </div>
                </template>
              </Card>
            </div>
            
            <div v-else class="empty-state">
              <p>No options defined. Add options to create player choices.</p>
            </div>
          </div>
        </TabPanel>
        
        <TabPanel header="Events">
          <div class="events-section">
            <div class="section-header">
              <h4>Node Events</h4>
              <Button 
                icon="pi pi-plus" 
                label="Add Event"
                size="small"
                @click="addEvent"
              />
            </div>
            
            <div v-if="editData.Events?.length" class="events-list">
              <Card v-for="(event, index) in editData.Events" :key="index" class="event-card">
                <template #content>
                  <div class="event-header">
                    <span class="event-number">{{ index + 1 }}</span>
                    <Button 
                      icon="pi pi-trash" 
                      text 
                      severity="danger"
                      size="small"
                      @click="removeEvent(index)"
                    />
                  </div>
                  
                  <div class="form-group">
                    <label>Event Type</label>
                    <Dropdown 
                      v-model="event.Type" 
                      :options="eventTypes"
                      option-label="label"
                      option-value="value"
                      placeholder="Select event type"
                    />
                  </div>
                  
                  <div class="form-group">
                    <label>Arguments</label>
                    <InputText 
                      v-model="event.argsString" 
                      placeholder="Comma-separated arguments"
                      @input="updateEventArgs(event, $event)"
                    />
                  </div>
                </template>
              </Card>
            </div>
            
            <div v-else class="empty-state">
              <p>No events defined. Events are triggered when entering this node.</p>
            </div>
          </div>
        </TabPanel>
        
        <TabPanel header="Conditions">
          <div class="conditions-section">
            <div class="section-header">
              <h4>Node Conditions</h4>
              <Button 
                icon="pi pi-plus" 
                label="Add Condition"
                size="small"
                @click="addCondition"
              />
            </div>
            
            <div v-if="editData.Conditions?.length" class="conditions-list">
              <Card v-for="(condition, index) in editData.Conditions" :key="index" class="condition-card">
                <template #content>
                  <div class="condition-header">
                    <span class="condition-number">{{ index + 1 }}</span>
                    <Button 
                      icon="pi pi-trash" 
                      text 
                      severity="danger"
                      size="small"
                      @click="removeCondition(index)"
                    />
                  </div>
                  
                  <div class="form-group">
                    <label>Condition Type</label>
                    <Dropdown 
                      v-model="condition.Type" 
                      :options="conditionTypes"
                      option-label="label"
                      option-value="value"
                      placeholder="Select condition type"
                    />
                  </div>
                  
                  <div class="form-group">
                    <label>Arguments</label>
                    <InputText 
                      v-model="condition.argsString" 
                      placeholder="Comma-separated arguments"
                      @input="updateConditionArgs(condition, $event)"
                    />
                  </div>
                </template>
              </Card>
            </div>
            
            <div v-else class="empty-state">
              <p>No conditions defined. Conditions determine if this node is available.</p>
            </div>
          </div>
        </TabPanel>
      </TabView>
    </div>
    
    <div class="editor-footer">
      <Button 
        label="Cancel" 
        severity="secondary"
        @click="cancelEdit"
      />
      <Button 
        label="Save Changes" 
        @click="saveChanges"
        :disabled="!isValid"
      />
    </div>
  </div>
  
  <div v-else class="no-selection">
    <div class="no-selection-content">
      <i class="pi pi-info-circle"></i>
      <h3>No Node Selected</h3>
      <p>Select a node from the graph to edit its properties, or right-click to add a new node.</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useDialogueStore } from '@/stores/dialogueStore'
import type { DialogueNode, DialogueOption, DialogueEvent, DialogueCondition } from '@/types/dialogue'

const emit = defineEmits<{
  close: []
}>()

const store = useDialogueStore()

// Form data
const editData = ref<DialogueNode & { id?: string }>({
  Text: '',
  Options: [],
  Events: [],
  Conditions: []
})

// Dropdown options
const optionTypes = [
  { label: 'Go to Node', value: 'NODE' },
  { label: 'Trigger Event', value: 'EVENT' },
  { label: 'Exit Dialogue', value: 'EXIT' }
]

const eventTypes = [
  { label: 'NPC Event', value: 'NpcEvent' },
  { label: 'Give Item', value: 'GiveItem' },
  { label: 'Set Flag', value: 'SetFlag' },
  { label: 'Play Sound', value: 'PlaySound' },
  { label: 'Custom', value: 'Custom' }
]

const conditionTypes = [
  { label: 'Has Item', value: 'HasItem' },
  { label: 'Flag True', value: 'FlagTrue' },
  { label: 'Flag False', value: 'FlagFalse' },
  { label: 'Custom', value: 'Custom' }
]

// Computed properties
const selectedNode = computed(() => store.selectedNode)

const isValid = computed(() => {
  return editData.value.Text.trim().length > 0
})

// Watch for selected node changes
watch(selectedNode, (newNode) => {
  if (newNode) {
    loadNodeData(newNode)
  }
}, { immediate: true })

// Methods
function loadNodeData(node: any) {
  editData.value = {
    ...node.data,
    id: node.id,
    Options: node.data.Options?.map((opt: DialogueOption) => ({
      ...opt,
      Argument: opt.Type === 'EVENT' && !opt.Argument ? { Type: 'Custom', Args: [] } : opt.Argument
    })) || [],
    Events: node.data.Events?.map((evt: DialogueEvent & { argsString?: string }) => ({
      ...evt,
      argsString: evt.Args?.join(', ') || ''
    })) || [],
    Conditions: node.data.Conditions?.map((cond: DialogueCondition & { argsString?: string }) => ({
      ...cond,
      argsString: cond.Args?.join(', ') || ''
    })) || []
  }
}

function addOption() {
  if (!editData.value.Options) {
    editData.value.Options = []
  }
  editData.value.Options.push({
    Text: '',
    Type: 'EXIT'
  })
}

function removeOption(index: number) {
  editData.value.Options?.splice(index, 1)
}

function addEvent() {
  if (!editData.value.Events) {
    editData.value.Events = []
  }
  editData.value.Events.push({
    Type: 'Custom',
    Args: [],
    argsString: ''
  } as any)
}

function removeEvent(index: number) {
  editData.value.Events?.splice(index, 1)
}

function updateEventArgs(event: any, inputEvent: Event) {
  const target = inputEvent.target as HTMLInputElement
  event.argsString = target.value
  event.Args = target.value.split(',').map(arg => arg.trim()).filter(arg => arg)
}

function addCondition() {
  if (!editData.value.Conditions) {
    editData.value.Conditions = []
  }
  editData.value.Conditions.push({
    Type: 'Custom',
    Args: [],
    argsString: ''
  } as any)
}

function removeCondition(index: number) {
  editData.value.Conditions?.splice(index, 1)
}

function updateConditionArgs(condition: any, inputEvent: Event) {
  const target = inputEvent.target as HTMLInputElement
  condition.argsString = target.value
  condition.Args = target.value.split(',').map(arg => arg.trim()).filter(arg => arg)
}

function saveChanges() {
  if (!selectedNode.value || !isValid.value) return
  
  // Clean up the data before saving
  const cleanData: DialogueNode = {
    ...editData.value,
    Options: editData.value.Options?.map(opt => ({
      Text: opt.Text,
      Type: opt.Type,
      Argument: opt.Argument,
      Conditions: opt.Conditions
    })),
    Events: editData.value.Events?.map((evt: any) => ({
      Type: evt.Type,
      Args: evt.Args || []
    })),
    Conditions: editData.value.Conditions?.map((cond: any) => ({
      Type: cond.Type,
      Args: cond.Args || []
    }))
  }
  
  // Remove empty arrays
  if (!cleanData.Options?.length) delete cleanData.Options
  if (!cleanData.Events?.length) delete cleanData.Events
  if (!cleanData.Conditions?.length) delete cleanData.Conditions
  
  store.updateNode(selectedNode.value.id, cleanData)
}

function cancelEdit() {
  if (selectedNode.value) {
    loadNodeData(selectedNode.value)
  }
}

function closeEditor() {
  store.selectNode(null)
  emit('close')
}
</script>

<style scoped>
.node-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #2d2d2d;
  color: #ffffff;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #4a5568;
}

.editor-header h3 {
  margin: 0;
  color: #1e293b;
}

.editor-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  color: #374151;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h4 {
  margin: 0;
  color: #1e293b;
}

.option-card,
.event-card,
.condition-card {
  margin-bottom: 12px;
}

.option-header,
.event-header,
.condition-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.option-number,
.event-number,
.condition-number {
  background: #3b82f6;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.empty-state {
  text-align: center;
  padding: 32px;
  color: #6b7280;
}

.editor-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 16px;
  border-top: 1px solid #e2e8f0;
}

.no-selection {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--surface-ground);
}

.no-selection-content {
  text-align: center;
  color: #6b7280;
}

.no-selection-content i {
  font-size: 48px;
  margin-bottom: 16px;
  color: #d1d5db;
}

.no-selection-content h3 {
  margin: 0 0 8px 0;
  color: #374151;
}

.no-selection-content p {
  margin: 0;
  max-width: 300px;
}
</style>

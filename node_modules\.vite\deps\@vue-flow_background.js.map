{"version": 3, "sources": ["../../@vue-flow/background/dist/vue-flow-background.mjs"], "sourcesContent": ["import { h, defineComponent, computed, toRef, openBlock, createElementBlock, normalizeStyle, renderSlot, createElementVNode, unref, createBlock, createCommentVNode } from \"vue\";\nimport { useVueFlow } from \"@vue-flow/core\";\nvar BackgroundVariant = /* @__PURE__ */ ((BackgroundVariant2) => {\n  BackgroundVariant2[\"Lines\"] = \"lines\";\n  BackgroundVariant2[\"Dots\"] = \"dots\";\n  return BackgroundVariant2;\n})(BackgroundVariant || {});\nconst LinePattern = function({ dimensions, size, color }) {\n  return h(\"path\", {\n    \"stroke\": color,\n    \"stroke-width\": size,\n    \"d\": `M${dimensions[0] / 2} 0 V${dimensions[1]} M0 ${dimensions[1] / 2} H${dimensions[0]}`\n  });\n};\nconst DotPattern = function({ radius, color }) {\n  return h(\"circle\", { cx: radius, cy: radius, r: radius, fill: color });\n};\n({\n  [BackgroundVariant.Lines]: LinePattern,\n  [BackgroundVariant.Dots]: DotPattern\n});\nconst DefaultBgColors = {\n  [BackgroundVariant.Dots]: \"#81818a\",\n  [BackgroundVariant.Lines]: \"#eee\"\n};\nconst _hoisted_1 = [\"id\", \"x\", \"y\", \"width\", \"height\", \"patternTransform\"];\nconst _hoisted_2 = {\n  key: 2,\n  height: \"100\",\n  width: \"100\"\n};\nconst _hoisted_3 = [\"fill\"];\nconst _hoisted_4 = [\"x\", \"y\", \"fill\"];\nconst __default__ = {\n  name: \"Background\",\n  compatConfig: { MODE: 3 }\n};\nconst _sfc_main = /* @__PURE__ */ defineComponent({\n  ...__default__,\n  props: {\n    id: {},\n    variant: { default: () => BackgroundVariant.Dots },\n    gap: { default: 20 },\n    size: { default: 1 },\n    lineWidth: { default: 1 },\n    patternColor: {},\n    color: {},\n    bgColor: {},\n    height: { default: 100 },\n    width: { default: 100 },\n    x: { default: 0 },\n    y: { default: 0 },\n    offset: { default: 0 }\n  },\n  setup(__props) {\n    const { id: vueFlowId, viewport } = useVueFlow();\n    const background = computed(() => {\n      const zoom = viewport.value.zoom;\n      const [gapX, gapY] = Array.isArray(__props.gap) ? __props.gap : [__props.gap, __props.gap];\n      const scaledGap = [gapX * zoom || 1, gapY * zoom || 1];\n      const scaledSize = __props.size * zoom;\n      const [offsetX, offsetY] = Array.isArray(__props.offset) ? __props.offset : [__props.offset, __props.offset];\n      const scaledOffset = [offsetX * zoom || 1 + scaledGap[0] / 2, offsetY * zoom || 1 + scaledGap[1] / 2];\n      return {\n        scaledGap,\n        offset: scaledOffset,\n        size: scaledSize\n      };\n    });\n    const patternId = toRef(() => `pattern-${vueFlowId}${__props.id ? `-${__props.id}` : \"\"}`);\n    const patternColor = toRef(() => __props.color || __props.patternColor || DefaultBgColors[__props.variant || BackgroundVariant.Dots]);\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"svg\", {\n        class: \"vue-flow__background vue-flow__container\",\n        style: normalizeStyle({\n          height: `${_ctx.height > 100 ? 100 : _ctx.height}%`,\n          width: `${_ctx.width > 100 ? 100 : _ctx.width}%`\n        })\n      }, [\n        renderSlot(_ctx.$slots, \"pattern-container\", { id: patternId.value }, () => [\n          createElementVNode(\"pattern\", {\n            id: patternId.value,\n            x: unref(viewport).x % background.value.scaledGap[0],\n            y: unref(viewport).y % background.value.scaledGap[1],\n            width: background.value.scaledGap[0],\n            height: background.value.scaledGap[1],\n            patternTransform: `translate(-${background.value.offset[0]},-${background.value.offset[1]})`,\n            patternUnits: \"userSpaceOnUse\"\n          }, [\n            renderSlot(_ctx.$slots, \"pattern\", {}, () => [\n              _ctx.variant === unref(BackgroundVariant).Lines ? (openBlock(), createBlock(unref(LinePattern), {\n                key: 0,\n                size: _ctx.lineWidth,\n                color: patternColor.value,\n                dimensions: background.value.scaledGap\n              }, null, 8, [\"size\", \"color\", \"dimensions\"])) : _ctx.variant === unref(BackgroundVariant).Dots ? (openBlock(), createBlock(unref(DotPattern), {\n                key: 1,\n                color: patternColor.value,\n                radius: background.value.size / 2\n              }, null, 8, [\"color\", \"radius\"])) : createCommentVNode(\"\", true),\n              _ctx.bgColor ? (openBlock(), createElementBlock(\"svg\", _hoisted_2, [\n                createElementVNode(\"rect\", {\n                  width: \"100%\",\n                  height: \"100%\",\n                  fill: _ctx.bgColor\n                }, null, 8, _hoisted_3)\n              ])) : createCommentVNode(\"\", true)\n            ])\n          ], 8, _hoisted_1)\n        ]),\n        createElementVNode(\"rect\", {\n          x: _ctx.x,\n          y: _ctx.y,\n          width: \"100%\",\n          height: \"100%\",\n          fill: `url(#${patternId.value})`\n        }, null, 8, _hoisted_4),\n        renderSlot(_ctx.$slots, \"default\", { id: patternId.value })\n      ], 4);\n    };\n  }\n});\nexport {\n  _sfc_main as Background,\n  BackgroundVariant\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAEA,IAAI,qBAAqC,CAAC,uBAAuB;AAC/D,qBAAmB,OAAO,IAAI;AAC9B,qBAAmB,MAAM,IAAI;AAC7B,SAAO;AACT,GAAG,qBAAqB,CAAC,CAAC;AAC1B,IAAM,cAAc,SAAS,EAAE,YAAY,MAAM,MAAM,GAAG;AACxD,SAAO,EAAE,QAAQ;AAAA,IACf,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,KAAK,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,WAAW,CAAC,CAAC,OAAO,WAAW,CAAC,IAAI,CAAC,KAAK,WAAW,CAAC,CAAC;AAAA,EAC1F,CAAC;AACH;AACA,IAAM,aAAa,SAAS,EAAE,QAAQ,MAAM,GAAG;AAC7C,SAAO,EAAE,UAAU,EAAE,IAAI,QAAQ,IAAI,QAAQ,GAAG,QAAQ,MAAM,MAAM,CAAC;AACvE;AAAA,CACC;AAAA,EACC,CAAC,kBAAkB,KAAK,GAAG;AAAA,EAC3B,CAAC,kBAAkB,IAAI,GAAG;AAC5B;AACA,IAAM,kBAAkB;AAAA,EACtB,CAAC,kBAAkB,IAAI,GAAG;AAAA,EAC1B,CAAC,kBAAkB,KAAK,GAAG;AAC7B;AACA,IAAM,aAAa,CAAC,MAAM,KAAK,KAAK,SAAS,UAAU,kBAAkB;AACzE,IAAM,aAAa;AAAA,EACjB,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAM,aAAa,CAAC,MAAM;AAC1B,IAAM,aAAa,CAAC,KAAK,KAAK,MAAM;AACpC,IAAM,cAAc;AAAA,EAClB,MAAM;AAAA,EACN,cAAc,EAAE,MAAM,EAAE;AAC1B;AACA,IAAM,YAA4B,gBAAgB;AAAA,EAChD,GAAG;AAAA,EACH,OAAO;AAAA,IACL,IAAI,CAAC;AAAA,IACL,SAAS,EAAE,SAAS,MAAM,kBAAkB,KAAK;AAAA,IACjD,KAAK,EAAE,SAAS,GAAG;AAAA,IACnB,MAAM,EAAE,SAAS,EAAE;AAAA,IACnB,WAAW,EAAE,SAAS,EAAE;AAAA,IACxB,cAAc,CAAC;AAAA,IACf,OAAO,CAAC;AAAA,IACR,SAAS,CAAC;AAAA,IACV,QAAQ,EAAE,SAAS,IAAI;AAAA,IACvB,OAAO,EAAE,SAAS,IAAI;AAAA,IACtB,GAAG,EAAE,SAAS,EAAE;AAAA,IAChB,GAAG,EAAE,SAAS,EAAE;AAAA,IAChB,QAAQ,EAAE,SAAS,EAAE;AAAA,EACvB;AAAA,EACA,MAAM,SAAS;AACb,UAAM,EAAE,IAAI,WAAW,SAAS,IAAI,WAAW;AAC/C,UAAM,aAAa,SAAS,MAAM;AAChC,YAAM,OAAO,SAAS,MAAM;AAC5B,YAAM,CAAC,MAAM,IAAI,IAAI,MAAM,QAAQ,QAAQ,GAAG,IAAI,QAAQ,MAAM,CAAC,QAAQ,KAAK,QAAQ,GAAG;AACzF,YAAM,YAAY,CAAC,OAAO,QAAQ,GAAG,OAAO,QAAQ,CAAC;AACrD,YAAM,aAAa,QAAQ,OAAO;AAClC,YAAM,CAAC,SAAS,OAAO,IAAI,MAAM,QAAQ,QAAQ,MAAM,IAAI,QAAQ,SAAS,CAAC,QAAQ,QAAQ,QAAQ,MAAM;AAC3G,YAAM,eAAe,CAAC,UAAU,QAAQ,IAAI,UAAU,CAAC,IAAI,GAAG,UAAU,QAAQ,IAAI,UAAU,CAAC,IAAI,CAAC;AACpG,aAAO;AAAA,QACL;AAAA,QACA,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,IACF,CAAC;AACD,UAAM,YAAY,MAAM,MAAM,WAAW,SAAS,GAAG,QAAQ,KAAK,IAAI,QAAQ,EAAE,KAAK,EAAE,EAAE;AACzF,UAAM,eAAe,MAAM,MAAM,QAAQ,SAAS,QAAQ,gBAAgB,gBAAgB,QAAQ,WAAW,kBAAkB,IAAI,CAAC;AACpI,WAAO,CAAC,MAAM,WAAW;AACvB,aAAO,UAAU,GAAG,mBAAmB,OAAO;AAAA,QAC5C,OAAO;AAAA,QACP,OAAO,eAAe;AAAA,UACpB,QAAQ,GAAG,KAAK,SAAS,MAAM,MAAM,KAAK,MAAM;AAAA,UAChD,OAAO,GAAG,KAAK,QAAQ,MAAM,MAAM,KAAK,KAAK;AAAA,QAC/C,CAAC;AAAA,MACH,GAAG;AAAA,QACD,WAAW,KAAK,QAAQ,qBAAqB,EAAE,IAAI,UAAU,MAAM,GAAG,MAAM;AAAA,UAC1E,gBAAmB,WAAW;AAAA,YAC5B,IAAI,UAAU;AAAA,YACd,GAAG,MAAM,QAAQ,EAAE,IAAI,WAAW,MAAM,UAAU,CAAC;AAAA,YACnD,GAAG,MAAM,QAAQ,EAAE,IAAI,WAAW,MAAM,UAAU,CAAC;AAAA,YACnD,OAAO,WAAW,MAAM,UAAU,CAAC;AAAA,YACnC,QAAQ,WAAW,MAAM,UAAU,CAAC;AAAA,YACpC,kBAAkB,cAAc,WAAW,MAAM,OAAO,CAAC,CAAC,KAAK,WAAW,MAAM,OAAO,CAAC,CAAC;AAAA,YACzF,cAAc;AAAA,UAChB,GAAG;AAAA,YACD,WAAW,KAAK,QAAQ,WAAW,CAAC,GAAG,MAAM;AAAA,cAC3C,KAAK,YAAY,MAAM,iBAAiB,EAAE,SAAS,UAAU,GAAG,YAAY,MAAM,WAAW,GAAG;AAAA,gBAC9F,KAAK;AAAA,gBACL,MAAM,KAAK;AAAA,gBACX,OAAO,aAAa;AAAA,gBACpB,YAAY,WAAW,MAAM;AAAA,cAC/B,GAAG,MAAM,GAAG,CAAC,QAAQ,SAAS,YAAY,CAAC,KAAK,KAAK,YAAY,MAAM,iBAAiB,EAAE,QAAQ,UAAU,GAAG,YAAY,MAAM,UAAU,GAAG;AAAA,gBAC5I,KAAK;AAAA,gBACL,OAAO,aAAa;AAAA,gBACpB,QAAQ,WAAW,MAAM,OAAO;AAAA,cAClC,GAAG,MAAM,GAAG,CAAC,SAAS,QAAQ,CAAC,KAAK,mBAAmB,IAAI,IAAI;AAAA,cAC/D,KAAK,WAAW,UAAU,GAAG,mBAAmB,OAAO,YAAY;AAAA,gBACjE,gBAAmB,QAAQ;AAAA,kBACzB,OAAO;AAAA,kBACP,QAAQ;AAAA,kBACR,MAAM,KAAK;AAAA,gBACb,GAAG,MAAM,GAAG,UAAU;AAAA,cACxB,CAAC,KAAK,mBAAmB,IAAI,IAAI;AAAA,YACnC,CAAC;AAAA,UACH,GAAG,GAAG,UAAU;AAAA,QAClB,CAAC;AAAA,QACD,gBAAmB,QAAQ;AAAA,UACzB,GAAG,KAAK;AAAA,UACR,GAAG,KAAK;AAAA,UACR,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,MAAM,QAAQ,UAAU,KAAK;AAAA,QAC/B,GAAG,MAAM,GAAG,UAAU;AAAA,QACtB,WAAW,KAAK,QAAQ,WAAW,EAAE,IAAI,UAAU,MAAM,CAAC;AAAA,MAC5D,GAAG,CAAC;AAAA,IACN;AAAA,EACF;AACF,CAAC;", "names": []}
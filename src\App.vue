<template>
  <div class="app">
    <Toast />
    <ConfirmDialog />
    
    <!-- Main Toolbar -->
    <Toolbar class="main-toolbar">
      <template #start>
        <div class="toolbar-section">
          <Button 
            icon="pi pi-file" 
            label="New"
            @click="newDialogue"
            class="p-button-sm"
          />
          <Button 
            icon="pi pi-folder-open" 
            label="Open"
            @click="openFile"
            class="p-button-sm"
          />
          <Button 
            icon="pi pi-save" 
            label="Save"
            @click="saveFile"
            :disabled="!store.isDirty"
            class="p-button-sm"
          />
          
          <div class="toolbar-divider"></div>
          
          <Button 
            icon="pi pi-undo" 
            @click="store.undo"
            :disabled="!store.canUndo"
            v-tooltip="'Undo (Ctrl+Z)'"
            class="p-button-sm"
          />
          <Button 
            icon="pi pi-redo" 
            @click="store.redo"
            :disabled="!store.canRedo"
            v-tooltip="'Redo (Ctrl+Y)'"
            class="p-button-sm"
          />
          
          <div class="toolbar-divider"></div>
          
          <Button 
            icon="pi pi-download" 
            label="Export Lua"
            @click="exportLua"
            class="p-button-sm"
          />
          <Button 
            icon="pi pi-file-export" 
            label="Export JSON"
            @click="exportJson"
            class="p-button-sm"
          />
        </div>
      </template>
      
      <template #end>
        <div class="toolbar-section">
          <Button
            icon="pi pi-question-circle"
            @click="showHelp"
            v-tooltip="'Help'"
            text
            class="p-button-sm"
          />
        </div>
      </template>
    </Toolbar>

    <!-- Main Content -->
    <div class="main-content">
      <Splitter style="height: 100%">
        <SplitterPanel :size="75" :min-size="50">
          <div class="graph-panel">
            <NodeGraph @edit-node="editNode" />
          </div>
        </SplitterPanel>
        
        <SplitterPanel :size="25" :min-size="20">
          <div class="editor-panel">
            <NodeEditor @close="closeEditor" />
          </div>
        </SplitterPanel>
      </Splitter>
    </div>

    <!-- File input for opening files -->
    <input
      ref="fileInput"
      type="file"
      accept=".json"
      @change="handleFileSelect"
      style="display: none"
    />

    <!-- Help Dialog -->
    <Dialog 
      v-model:visible="showHelpDialog" 
      header="Help"
      :style="{ width: '600px' }"
      modal
    >
      <div class="help-content">
        <h3>Getting Started</h3>
        <ul>
          <li>Right-click on the canvas to add a new dialogue node</li>
          <li>Click on a node to select and edit it</li>
          <li>Drag from connection points to link nodes</li>
          <li>Use the toolbar to save, load, and export your dialogue trees</li>
        </ul>
        
        <h3>Keyboard Shortcuts</h3>
        <ul>
          <li><strong>Ctrl+Z</strong> - Undo</li>
          <li><strong>Ctrl+Y</strong> - Redo</li>
          <li><strong>Ctrl+S</strong> - Save</li>
          <li><strong>Ctrl+O</strong> - Open</li>
          <li><strong>Ctrl+N</strong> - New</li>
          <li><strong>Delete</strong> - Delete selected node</li>
        </ul>
        
        <h3>Node Properties</h3>
        <ul>
          <li><strong>Text</strong> - The dialogue text spoken by the NPC</li>
          <li><strong>Options</strong> - Player choices that can lead to other nodes or trigger events</li>
          <li><strong>Events</strong> - Actions triggered when entering the node</li>
          <li><strong>Conditions</strong> - Requirements for the node to be available</li>
        </ul>
      </div>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useToast } from 'primevue/usetoast'
import { useConfirm } from 'primevue/useconfirm'
import NodeGraph from './components/NodeGraph.vue'
import NodeEditor from './components/NodeEditor.vue'
import { useDialogueStore } from './stores/dialogueStore'
import { FileManager } from './utils/fileManager'

const store = useDialogueStore()
const toast = useToast()
const confirm = useConfirm()

// Refs
const fileInput = ref<HTMLInputElement>()
const showHelpDialog = ref(false)


// Methods
function newDialogue() {
  if (store.isDirty) {
    confirm.require({
      message: 'You have unsaved changes. Are you sure you want to create a new dialogue?',
      header: 'Confirm New',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        store.createNewTree()
        toast.add({ severity: 'success', summary: 'New Dialogue', detail: 'Created new dialogue tree' })
      }
    })
  } else {
    store.createNewTree()
    toast.add({ severity: 'success', summary: 'New Dialogue', detail: 'Created new dialogue tree' })
  }
}

function openFile() {
  if (store.isDirty) {
    confirm.require({
      message: 'You have unsaved changes. Are you sure you want to open a file?',
      header: 'Confirm Open',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        fileInput.value?.click()
      }
    })
  } else {
    fileInput.value?.click()
  }
}

async function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (file) {
    try {
      const tree = await FileManager.loadDialogueTree(file)
      store.loadDialogueTree(tree)
      toast.add({ severity: 'success', summary: 'File Loaded', detail: `Loaded ${tree.name}` })
    } catch (error) {
      toast.add({ 
        severity: 'error', 
        summary: 'Load Error', 
        detail: error instanceof Error ? error.message : 'Failed to load file' 
      })
    }
    
    // Reset file input
    target.value = ''
  }
}

async function saveFile() {
  try {
    await FileManager.saveDialogueTree(store.dialogueTree)
    store.isDirty = false
    toast.add({ severity: 'success', summary: 'File Saved', detail: `Saved ${store.dialogueTree.name}` })
  } catch (error) {
    toast.add({ 
      severity: 'error', 
      summary: 'Save Error', 
      detail: error instanceof Error ? error.message : 'Failed to save file' 
    })
  }
}

function exportLua() {
  try {
    FileManager.exportToLua(store.dialogueTree)
    toast.add({ severity: 'success', summary: 'Export Complete', detail: 'Exported to Lua format' })
  } catch (error) {
    toast.add({ 
      severity: 'error', 
      summary: 'Export Error', 
      detail: error instanceof Error ? error.message : 'Failed to export' 
    })
  }
}

function exportJson() {
  try {
    FileManager.exportToJson(store.dialogueTree)
    toast.add({ severity: 'success', summary: 'Export Complete', detail: 'Exported to JSON format' })
  } catch (error) {
    toast.add({ 
      severity: 'error', 
      summary: 'Export Error', 
      detail: error instanceof Error ? error.message : 'Failed to export' 
    })
  }
}

function editNode(nodeId: string) {
  store.selectNode(nodeId)
}

function closeEditor() {
  // Editor handles its own closing
}

function showHelp() {
  showHelpDialog.value = true
}



// Keyboard shortcuts
function handleKeydown(event: KeyboardEvent) {
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 'z':
        event.preventDefault()
        store.undo()
        break
      case 'y':
        event.preventDefault()
        store.redo()
        break
      case 's':
        event.preventDefault()
        saveFile()
        break
      case 'o':
        event.preventDefault()
        openFile()
        break
      case 'n':
        event.preventDefault()
        newDialogue()
        break
    }
  } else if (event.key === 'Delete' && store.selectedNodeId) {
    store.deleteNode(store.selectedNodeId)
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #1e1e1e;
  color: #ffffff;
}

.main-toolbar {
  border-bottom: 1px solid #374151;
  background: #2d2d2d;
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.app-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.toolbar-divider {
  width: 1px;
  height: 24px;
  background: #e2e8f0;
  margin: 0 8px;
}

.main-content {
  flex: 1;
  overflow: hidden;
  position: relative;
  width: 100%;
  height: 100%;
}

.graph-panel,
.editor-panel {
  height: 100%;
  background: #2d2d2d;
  position: relative;
  overflow: hidden;
}

.editor-panel {
  border-left: 1px solid #374151;
}

.help-content h3 {
  color: #1e293b;
  margin-top: 20px;
  margin-bottom: 10px;
}

.help-content h3:first-child {
  margin-top: 0;
}

.help-content ul {
  margin-bottom: 16px;
}

.help-content li {
  margin-bottom: 4px;
}
</style>

<style>
/* Global styles */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #1e1e1e;
  color: #ffffff;
  overflow: hidden;
}

html {
  height: 100%;
  overflow: hidden;
}

#app {
  height: 100vh;
  overflow: hidden;
}

/* Ensure Vue Flow containers are properly contained */
.p-splitter-panel {
  overflow: hidden !important;
}

.p-splitter {
  overflow: hidden !important;
}

.p-splitter {
  border: none;
}

.p-splitter-gutter {
  background: #e2e8f0;
}

.p-splitter-gutter-handle {
  background: #94a3b8;
}
</style>
